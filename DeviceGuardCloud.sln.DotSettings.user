<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AAllowNullAttribute_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F2b90705926ae4f10aa01bbe3f1025828c90908_003Fe4_003F6d3493ee_003FAllowNullAttribute_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AAuthAppBuilderExtensions_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2024_002E3_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F05148af0b00140579bbb67d69a63903e26918_003F31_003Ff3736073_003FAuthAppBuilderExtensions_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AAuthenticationServiceDependencyInjection_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2024_002E3_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F883dffa90d3d40b591672c2874b11aca8400_003Fab_003Fee48c135_003FAuthenticationServiceDependencyInjection_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AConcurrentDictionary_00602_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F02c63a6af72b4a509588e480e7c0b57343918_003Fef_003F3fec8f56_003FConcurrentDictionary_00602_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AControllerBase_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F51550674e7644fa2a26799b46b68aa471de928_003F41_003Fdd8c64bf_003FControllerBase_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003ADbSet_00601_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F86986e3b5ab142e1a33017545ca2e53926a820_003F45_003F7c0c1bc3_003FDbSet_00601_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003ADistributedCacheExtensions_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F51bdb2654e8f4169b707ee2bc215de36e930_003F5a_003F371606bf_003FDistributedCacheExtensions_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AEntityFrameworkCoreRepository_00602_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F51b6be2575054d5893f4075483a2ef573a00_003F07_003F9f1ccac4_003FEntityFrameworkCoreRepository_00602_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AHttpClient_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2024_002E3_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003Fdf89d0af66864d5383ccf833a24323de1a6938_003F66_003Fa6b5a657_003FHttpClient_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AIDistributedCache_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F51bdb2654e8f4169b707ee2bc215de36e930_003Fc9_003Fd5202dcd_003FIDistributedCache_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AILoginTokenPersistence_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F02e23721c67c465481d9a6800c83505f3c00_003F8f_003F4207562c_003FILoginTokenPersistence_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AIMemoryCache_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F51bdb2654e8f4169b707ee2bc215de36e930_003F3f_003F637831f5_003FIMemoryCache_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AIReadOnlyRepository_00602_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F514ace8be0a24c04865e6840ddea31912000_003F06_003F32254769_003FIReadOnlyRepository_00602_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AIRepository_00602_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F2b9d6886e87244f8b189fadc8be93af71e00_003F23_003Fa530beba_003FIRepository_00602_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AIRepository_00602_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003Fb56b6c9a44344afdbfb0555af8e36b201e00_003Fdb_003F116393f9_003FIRepository_00602_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AIRequestHandler_00601_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003Fe4918c8ac7be482996b82eec990f156d11200_003F28_003Fc517994f_003FIRequestHandler_00601_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AIUserTokenService_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F02e23721c67c465481d9a6800c83505f3c00_003F57_003Fb74f5e49_003FIUserTokenService_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AJwtClaimTypes_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F89e5a1be3ffc4bb1b2e6387dc8c6149920590_003F36_003F4be9e81f_003FJwtClaimTypes_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003ALicense_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003Fb1544761e64940be9ee333127d606513b6928_003Fdd_003Fe3a12aeb_003FLicense_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003ALoginTokenService_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2024_002E3_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F883dffa90d3d40b591672c2874b11aca8400_003Fa4_003F733c8552_003FLoginTokenService_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003ALoginTokenService_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F883dffa90d3d40b591672c2874b11aca8400_003F90_003Fd00d0a9c_003FLoginTokenService_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AModule_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F89e2fa23ab0143908c89b1901498628cc90920_003F97_003F634128e3_003FModule_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AMvcNewtonsoftJsonOptionsExtensions_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F96f94d0a84604a91bbbabdb5c991296a110b0_003F92_003F68d4990a_003FMvcNewtonsoftJsonOptionsExtensions_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003APageFilter_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F1871c0480c084ce68fc6b9b5ac63c2b36e00_003F47_003F18747d64_003FPageFilter_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003APage_00601_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F1871c0480c084ce68fc6b9b5ac63c2b36e00_003Ff4_003F3a214304_003FPage_00601_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003APlatformID_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F5aeb6167299d4cfba30c86624130edbcc90938_003Fcc_003F96cbb414_003FPlatformID_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AQueryableOrderExtensions_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F1cec44103dbd48f9a2f579d598206d808c00_003Ff4_003Febed92a6_003FQueryableOrderExtensions_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003ARelationalQueryableExtensions_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003Fd1186877370a4a8fb269316c8365629b1e55c0_003F78_003Fb471de7e_003FRelationalQueryableExtensions_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003ASaveMode_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F2b9d6886e87244f8b189fadc8be93af71e00_003F16_003Ff26a8036_003FSaveMode_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AStringEnumConverter_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F7e62198beab24380bbac29171862d1d8adf10_003F42_003F4e19b796_003FStringEnumConverter_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AUserToken_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2024_002E3_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F02e23721c67c465481d9a6800c83505f3c00_003F70_003F5fd5ba77_003FUserToken_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AUserToken_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F02e23721c67c465481d9a6800c83505f3c00_003F30_003F4222df8f_003FUserToken_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AValidationAttribute_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2025_002E1_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003Fdbbc8fe5df85473a91374e610639eb7331908_003Fec_003F2d09f933_003FValidationAttribute_002Ecs/@EntryIndexedValue">ForceIncluded</s:String>
	<s:String x:Key="/Default/Environment/AssemblyExplorer/XmlDocument/@EntryValue">&lt;AssemblyExplorer&gt;&#xD;
  &lt;Assembly Path="C:\Users\<USER>\.nuget\packages\mapster\7.4.0\lib\net7.0\Mapster.dll" /&gt;&#xD;
  &lt;Assembly Path="C:\Users\<USER>\.nuget\packages\azure.core\1.35.0\lib\net6.0\Azure.Core.dll" /&gt;&#xD;
  &lt;Assembly Path="C:\Users\<USER>\.nuget\packages\identitymodel\4.0.0\lib\netstandard2.0\IdentityModel.dll" /&gt;&#xD;
&lt;/AssemblyExplorer&gt;</s:String>
	<s:String x:Key="/Default/Environment/UnitTesting/UnitTestSessionStore/Sessions/=3d4647d8_002Dad8a_002D4044_002D879d_002D6f3610cddcec/@EntryIndexedValue">&lt;SessionState ContinuousTestingMode="0" IsActive="True" Name="TestMapping" xmlns="urn:schemas-jetbrains-com:jetbrains-ut-session"&gt;&#xD;
  &lt;TestAncestor&gt;&#xD;
    &lt;TestId&gt;xUnit::0E3204B8-199A-4E0F-9C74-EEA279470B35::net8.0::DeviceGuardCloud.Application.Queries.Tests.PluginDbQueryTests&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;xUnit::80E127CD-3E6E-466A-94C7-C8736B175923::net8.0::DeviceGuardCloud.Tests.PluginDbControllerTests&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;xUnit::0E3204B8-199A-4E0F-9C74-EEA279470B35::net8.0::DeviceGuardCloud.Application.Queries.Tests.HomePageRedirectionQuerySimpleTests&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;xUnit::0E3204B8-199A-4E0F-9C74-EEA279470B35::net8.0::DeviceGuardCloud.Application.Queries.Tests.HomePageRedirectionQueryTests&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;xUnit::6C0037D7-49C7-4FC9-AB2D-632C97A1A344::net8.0::DeviceGuardCloud.Application.Logic.Tests.UpdateHomePageRedirectionCommandHandlerSimpleTests&lt;/TestId&gt;&#xD;
    &lt;TestId&gt;xUnit::6C0037D7-49C7-4FC9-AB2D-632C97A1A344::net8.0::DeviceGuardCloud.Application.Logic.Tests.UpdateHomePageRedirectionCommandHandlerTests&lt;/TestId&gt;&#xD;
  &lt;/TestAncestor&gt;&#xD;
&lt;/SessionState&gt;</s:String>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=Mapster/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=Mediat/@EntryIndexedValue">True</s:Boolean>
	<s:String x:Key="/Default/CodeInspection/ExcludedFiles/FilesAndFoldersToSkip2/=7020124F_002D9FFC_002D4AC3_002D8F3D_002DAAB8E0240759_002Ff_003AIEquatable_00601_002Ecs_002Fl_003AC_0021_003FUsers_003FNepton_003FAppData_003FRoaming_003FJetBrains_003FRider2024_002E3_003Fresharper_002Dhost_003FDecompilerCache_003Fdecompiler_003F5aeb6167299d4cfba30c86624130edbcc90938_003Fd9_003F31dbd08c_003FIEquatable_00601_002Ecs/@EntryIndexedValue">ForceIncluded</s:String></wpf:ResourceDictionary>