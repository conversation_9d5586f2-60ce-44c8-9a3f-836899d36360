namespace IntegrationEventBus.IntegrationEventLogs;

/// <summary>
/// The repository for integration event log
/// </summary>
public interface IIntegrationEventLogRepository
{
    /// <summary>
    /// Get events waiting to be handled
    /// </summary>
    /// <param name="contextId"></param>
    /// <returns></returns>
    Task<IEnumerable<IntegrationEventLog>> GetEventsByContextIdAsync(Guid contextId);

    /// <summary>
    /// Save an event, which is usually called when you create a new event
    /// </summary>
    /// <param name="event"></param>
    /// <param name="contextId"></param>
    /// <returns></returns>
    Task SaveEventAsync(IntegrationEvent @event, Guid contextId);

    /// <summary>
    /// Remove processed event
    /// </summary>
    /// <param name="eventId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task RemoveEventAsync(Guid eventId, CancellationToken cancellationToken);
}
