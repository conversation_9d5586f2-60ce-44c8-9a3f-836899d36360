using Newtonsoft.Json;

#pragma warning disable CS8618 // Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.

namespace IntegrationEventBus.IntegrationEventLogs;

public class IntegrationEventLog
{
    private static readonly JsonSerializerSettings _jsonSerializerSettings = new()
    {
        TypeNameHandling = TypeNameHandling.All,
    };

    /// <summary>
    /// for ef core
    /// </summary>
    private IntegrationEventLog()
    {
    }

    public IntegrationEventLog(IntegrationEvent @event, Guid contextId)
    {
        if (@event == null) throw new ArgumentNullException(nameof(@event));

        Id            = @event.Id;
        CreatedAt     = @event.CreatedAt;
        EventTypeName = @event.GetType().FullName ?? "";
        Content       = JsonConvert.SerializeObject(@event, Formatting.Indented, _jsonSerializerSettings);
        TimesSent     = 0;
        ContextId     = contextId;
    }

    public Guid     Id            { get; private set; }
    public string   EventTypeName { get; private set; }
    public int      TimesSent     { get; set; }
    public DateTime CreatedAt     { get; private set; } = DateTime.Now;
    public string   Content       { get; private set; }

    /// <summary>
    /// The context id is used to identify the scope
    /// </summary>
    public Guid ContextId { get; private set; }

    public string? GetEventTypeShortName() => EventTypeName?.Split('.')?.Last();

    private IntegrationEvent? _eventCache;

    public IntegrationEvent? GetIntegrationEvent()
    {
        return _eventCache ??= JsonConvert.DeserializeObject(Content, _jsonSerializerSettings) as IntegrationEvent;
    }
}
