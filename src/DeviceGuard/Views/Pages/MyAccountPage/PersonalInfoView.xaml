<UserControl x:Class="DeviceGuard.Shell.Views.Pages.MyAccountPage.PersonalInfoView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:myAccountPage="clr-namespace:DeviceGuard.Shell.Views.Pages.MyAccountPage"
             mc:Ignorable="d"
             d:DesignHeight="550"
             d:DesignWidth="750"
             d:DataContext="{d:DesignInstance myAccountPage:PersonalInfoViewModel}">
    
    <UserControl.Resources>
        <Style x:Key="FormLabelStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>
        
        <Style x:Key="FormTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="Margin" Value="0,0,0,16"/>
            <Setter Property="materialDesign:HintAssist.IsFloating" Value="False"/>
            <Setter Property="materialDesign:TextFieldAssist.DecorationVisibility" Value="Hidden"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" Padding="30">
        <StackPanel MaxWidth="600">
            <!-- 页面标题 -->
            <StackPanel Margin="0,0,0,30">
                <TextBlock Text="个人信息" FontSize="28" FontWeight="Bold" Foreground="#333" Margin="0,0,0,8"/>
                <TextBlock Text="管理您的个人信息和账户设置" FontSize="14" Foreground="#666"/>
            </StackPanel>

            <!-- 头像区域 -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,40">
                <Grid>
                    <!-- 头像背景 -->
                    <Ellipse Width="100" Height="100" Fill="#E3F2FD"/>
                    
                    <!-- 头像文字 -->
                    <TextBlock Text="{Binding AvatarText}" 
                             FontSize="36" FontWeight="Bold" 
                             Foreground="#2196F3"
                             HorizontalAlignment="Center" 
                             VerticalAlignment="Center"/>
                    
                    <!-- 上传按钮 -->
                    <Button Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
                          Width="32" Height="32"
                          HorizontalAlignment="Right" VerticalAlignment="Bottom"
                          Margin="0,0,-8,-8"
                          Command="{Binding UploadAvatarCommand}"
                          ToolTip="更换头像">
                        <materialDesign:PackIcon Kind="Camera" Width="16" Height="16"/>
                    </Button>
                </Grid>
                
                <StackPanel Margin="20,0,0,0" VerticalAlignment="Center">
                    <TextBlock Text="{Binding DisplayName}" FontSize="20" FontWeight="Bold" Foreground="#333" Margin="0,0,0,4"/>
                    <TextBlock Text="{Binding LastLoginTime}" FontSize="12" Foreground="#666" Margin="0,0,0,8"/>
                    <Border Background="#4CAF50" CornerRadius="12" Padding="8,4">
                        <TextBlock Text="账户正常" FontSize="12" Foreground="White" FontWeight="Medium"/>
                    </Border>
                </StackPanel>
            </StackPanel>

            <!-- 表单区域 -->
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <!-- 登录名 -->
                    <TextBlock Text="登录名" Style="{StaticResource FormLabelStyle}"/>
                    <TextBox Text="{Binding LoginName}" 
                           Style="{StaticResource FormTextBoxStyle}"
                           IsReadOnly="True"
                           Background="#F5F5F5"/>

                    <!-- 手机号 -->
                    <TextBlock Text="手机号" Style="{StaticResource FormLabelStyle}"/>
                    <TextBox Text="{Binding PhoneNumber}" 
                           Style="{StaticResource FormTextBoxStyle}"
                           IsReadOnly="{Binding IsReadOnly}"/>
                </StackPanel>

                <StackPanel Grid.Column="2">
                    <!-- 用户名 -->
                    <TextBlock Text="用户名" Style="{StaticResource FormLabelStyle}"/>
                    <TextBox Text="{Binding UserName}" 
                           Style="{StaticResource FormTextBoxStyle}"
                           IsReadOnly="{Binding IsReadOnly}"/>

                    <!-- 邮箱地址 -->
                    <TextBlock Text="邮箱地址" Style="{StaticResource FormLabelStyle}"/>
                    <TextBox Text="{Binding Email}" 
                           Style="{StaticResource FormTextBoxStyle}"
                           IsReadOnly="{Binding IsReadOnly}"/>
                </StackPanel>
            </Grid>

            <!-- 个人简介 -->
            <TextBlock Text="个人简介" Style="{StaticResource FormLabelStyle}"/>
            <TextBox Text="{Binding Biography}" 
                   Style="{StaticResource FormTextBoxStyle}"
                   IsReadOnly="{Binding IsReadOnly}"
                   AcceptsReturn="True"
                   TextWrapping="Wrap"
                   MinHeight="80"
                   VerticalScrollBarVisibility="Auto"
                   materialDesign:HintAssist.Hint="介绍一下自己吧..."/>

            <!-- 操作按钮 -->
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
                <Button Content="取消" 
                      Style="{StaticResource MaterialDesignOutlinedButton}"
                      Command="{Binding CancelEditCommand}"
                      Visibility="{Binding IsEditing, Converter={StaticResource BooleanToVisibilityConverter}}"
                      Margin="0,0,10,0"
                      Padding="20,8"/>
                
                <Button Content="{Binding EditButtonText}" 
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Command="{Binding ToggleEditCommand}"
                      Padding="20,8">
                    <Button.Background>
                        <SolidColorBrush Color="{Binding EditButtonColor}"/>
                    </Button.Background>
                </Button>
            </StackPanel>
        </StackPanel>
    </ScrollViewer>
</UserControl>
