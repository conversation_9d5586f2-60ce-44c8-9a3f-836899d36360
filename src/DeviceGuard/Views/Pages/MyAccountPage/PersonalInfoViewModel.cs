using System.Windows.Media;
using DeviceGuard.Windows.Utility.Mvvm;

namespace DeviceGuard.Shell.Views.Pages.MyAccountPage;

public class PersonalInfoViewModel : ViewModelBase
{
    private bool _isEditing = false;
    private string _loginName = "testuser";
    private string _userName = "Test User";
    private string _phoneNumber = "138****8888";
    private string _email = "<EMAIL>";
    private string _biography = "这是一个测试用户的个人简介。";
    
    // 保存原始数据用于取消编辑
    private string _originalUserName = string.Empty;
    private string _originalPhoneNumber = string.Empty;
    private string _originalEmail = string.Empty;
    private string _originalBiography = string.Empty;

    public PersonalInfoViewModel()
    {
        // 初始化显示数据
        UpdateDisplayData();
    }

    public bool IsEditing
    {
        get => _isEditing;
        set => SetProperty(ref _isEditing, value);
    }

    public bool IsReadOnly => !IsEditing;

    public string LoginName
    {
        get => _loginName;
        set => SetProperty(ref _loginName, value);
    }

    public string UserName
    {
        get => _userName;
        set
        {
            if (SetProperty(ref _userName, value))
            {
                UpdateDisplayData();
            }
        }
    }

    public string PhoneNumber
    {
        get => _phoneNumber;
        set => SetProperty(ref _phoneNumber, value);
    }

    public string Email
    {
        get => _email;
        set => SetProperty(ref _email, value);
    }

    public string Biography
    {
        get => _biography;
        set => SetProperty(ref _biography, value);
    }

    public string DisplayName => UserName;
    
    public string AvatarText => string.IsNullOrEmpty(UserName) ? "U" : UserName.Substring(0, 1).ToUpper();
    
    public string LastLoginTime => "上次登录：2025年7月6日 14:32";

    public string EditButtonText => IsEditing ? "保存" : "编辑";
    
    public Color EditButtonColor => IsEditing ? Colors.Green : Color.FromRgb(0x21, 0x96, 0xF3);

    public DelegateCommand ToggleEditCommand => GetPropertyCached(() => new DelegateCommand(() =>
    {
        if (!IsEditing)
        {
            // 进入编辑模式，保存原始数据
            _originalUserName = UserName;
            _originalPhoneNumber = PhoneNumber;
            _originalEmail = Email;
            _originalBiography = Biography;
            
            IsEditing = true;
        }
        else
        {
            // 保存并退出编辑模式
            SaveProfile();
        }
    }));

    public DelegateCommand CancelEditCommand => GetPropertyCached(() => new DelegateCommand(() =>
    {
        if (!IsEditing) return;

        // 恢复原始数据
        UserName = _originalUserName;
        PhoneNumber = _originalPhoneNumber;
        Email = _originalEmail;
        Biography = _originalBiography;

        IsEditing = false;
    }));

    public DelegateCommand UploadAvatarCommand => GetPropertyCached(() => new DelegateCommand(() =>
    {
        // 模拟头像上传 - 这里可以实现实际的头像上传逻辑
        // 暂时只是触发属性更新
        RaisePropertyChanged(nameof(AvatarText));
    }));

    private void SaveProfile()
    {
        // 这里可以添加实际的保存逻辑，比如调用API保存到服务器
        // 目前只是模拟保存成功
        
        // 验证邮箱格式
        if (!string.IsNullOrEmpty(Email) && !IsValidEmail(Email))
        {
            // 这里应该显示错误消息，暂时忽略
            return;
        }

        IsEditing = false;
        
        // 清空原始数据
        _originalUserName = string.Empty;
        _originalPhoneNumber = string.Empty;
        _originalEmail = string.Empty;
        _originalBiography = string.Empty;
    }

    private void UpdateDisplayData()
    {
        RaisePropertyChanged(nameof(DisplayName));
        RaisePropertyChanged(nameof(AvatarText));
        RaisePropertyChanged(nameof(IsReadOnly));
        RaisePropertyChanged(nameof(EditButtonText));
        RaisePropertyChanged(nameof(EditButtonColor));
    }

    private bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }
}
