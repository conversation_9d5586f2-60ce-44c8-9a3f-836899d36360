using System.Collections.ObjectModel;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Shell.Views.LoginDialog;
using DeviceGuard.Windows.Utility.Dialogs;
using DeviceGuard.Windows.Utility.Mvvm;
using MaterialDesignThemes.Wpf;
using PropertyChanged;

namespace DeviceGuard.Shell.Views.Pages.MyAccountPage;

public class MyAccountPageViewModel : RegionViewModelBase
{
    private readonly ILoginSession   _session;
    private readonly LoginDialogView _loginDialogView;
    private readonly IDialog         _dialog;

    public MyAccountPageViewModel(ILoginSession session,
        LoginDialogView                         loginDialogView,
        IDialog                                 dialog
    )
    {
        _session         = session;
        _loginDialogView = loginDialogView;
        _dialog          = dialog;

        InitializeMenuItems();
    }

    private void InitializeMenuItems()
    {
        MenuItems = new ObservableCollection<AccountMenuItem>
        {
            new AccountMenuItem { Name = "个人信息", Icon = PackIconKind.Account, IsSelected = true, PageType = "PersonalInfo" },
            new AccountMenuItem { Name = "安全设置", Icon = PackIconKind.Security, IsSelected = false, PageType = "SecuritySettings" },
            new AccountMenuItem { Name = "设备管理", Icon = PackIconKind.Cellphone, IsSelected = false, PageType = "DeviceManagement" },
            new AccountMenuItem { Name = "通知设置", Icon = PackIconKind.Bell, IsSelected = false, PageType = "NotificationSettings" },
            new AccountMenuItem { Name = "账户信息", Icon = PackIconKind.CreditCard, IsSelected = false, PageType = "AccountInfo" },
            new AccountMenuItem { Name = "使用统计", Icon = PackIconKind.ChartLine, IsSelected = false, PageType = "UsageStatistics" },
            new AccountMenuItem { Name = "帮助中心", Icon = PackIconKind.Help, IsSelected = false, PageType = "HelpCenter" }
        };

        SelectedMenuItem = MenuItems.First();
        CurrentPageType = SelectedMenuItem.PageType;
    }

    public override async Task OnLoadedAsync(CancellationToken cancellation)
    {
        IsLoggedIn = await _session.IsLoggedInAsync(cancellation);
        LoginUser  = await _session.GetLoginUserAsync(cancellation) ?? new LoginUserInfo();
    }

    public bool IsLoggedIn { get; private set; }

    public LoginUserInfo? LoginUser { get; private set; } = new();

    public ObservableCollection<AccountMenuItem> MenuItems { get; private set; } = new();

    public AccountMenuItem? SelectedMenuItem { get; set; }

    public string CurrentPageType { get; set; } = "PersonalInfo";

    public DelegateCommand<AccountMenuItem> MenuItemClickCommand => GetPropertyCached(() =>
        new DelegateCommand<AccountMenuItem>(menuItem =>
        {
            if (menuItem == null) return;

            // 更新选中状态
            foreach (var item in MenuItems)
            {
                item.IsSelected = item == menuItem;
            }

            SelectedMenuItem = menuItem;
            CurrentPageType = menuItem.PageType;
        }));

    public AsyncDelegateCommand LoginCommand => GetPropertyCached(() => new AsyncDelegateCommand(async (cancellation) =>
        {
            if (await _dialog.ShowDialogAsync(_loginDialogView) != true)
            {
                return;
            }

            LoginUser  = await _session.GetLoginUserAsync(cancellation);
            IsLoggedIn = LoginUser != null;
        }
    ));

    public AsyncDelegateCommand SignOutCommand => GetPropertyCached(() => new AsyncDelegateCommand(async (cancellation) =>
        {
            await _session.LogoutAsync(cancellation);

            IsLoggedIn = await _session.IsLoggedInAsync(cancellation);
            LoginUser  = await _session.GetLoginUserAsync(cancellation);
        }
    ));
}

public class AccountMenuItem : ViewModelBase
{
    public string Name { get; set; } = string.Empty;
    public PackIconKind Icon { get; set; }
    public bool IsSelected { get; set; }
    public string PageType { get; set; } = string.Empty;
}
