using System.Windows.Input;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Windows.Infrastructure.ViewModels;
using DeviceGuard.Windows.Utility.Dialogs;
using Prism.Commands;
using Serilog;

namespace DeviceGuard.Shell.Views.Pages.MyAccountPage;

/// <summary>
/// 安全设置页面的ViewModel
/// </summary>
public class SecuritySettingsViewModel : ViewModelBase
{
    private readonly ILoginSession _loginSession;
    private readonly IMessageBox _messageBox;
    private readonly ILogger _logger = Log.ForContext<SecuritySettingsViewModel>();

    private string _phoneNumber = string.Empty;
    private string _newPhoneNumber = string.Empty;
    private string _emailAddress = string.Empty;
    private string _newEmailAddress = string.Empty;

    public SecuritySettingsViewModel(ILoginSession loginSession, IMessageBox messageBox)
    {
        _loginSession = loginSession ?? throw new ArgumentNullException(nameof(loginSession));
        _messageBox = messageBox ?? throw new ArgumentNullException(nameof(messageBox));

        // 初始化命令
        UpdatePhoneCommand = new DelegateCommand(OnUpdatePhone, CanUpdatePhone);
        UpdateEmailCommand = new DelegateCommand(OnUpdateEmail, CanUpdateEmail);
        ChangePasswordCommand = new DelegateCommand(OnChangePassword, CanChangePassword);

        // 加载用户数据
        _ = LoadUserDataAsync();
    }

    #region 属性

    /// <summary>
    /// 当前手机号码
    /// </summary>
    public string PhoneNumber
    {
        get => _phoneNumber;
        set
        {
            if (SetProperty(ref _phoneNumber, value))
            {
                RaisePropertyChanged();
            }
        }
    }

    /// <summary>
    /// 新手机号码
    /// </summary>
    public string NewPhoneNumber
    {
        get => _newPhoneNumber;
        set
        {
            if (SetProperty(ref _newPhoneNumber, value))
            {
                RaisePropertyChanged();
                UpdatePhoneCommand.RaiseCanExecuteChanged();
            }
        }
    }

    /// <summary>
    /// 当前邮箱地址
    /// </summary>
    public string EmailAddress
    {
        get => _emailAddress;
        set
        {
            if (SetProperty(ref _emailAddress, value))
            {
                RaisePropertyChanged();
            }
        }
    }

    /// <summary>
    /// 新邮箱地址
    /// </summary>
    public string NewEmailAddress
    {
        get => _newEmailAddress;
        set
        {
            if (SetProperty(ref _newEmailAddress, value))
            {
                RaisePropertyChanged();
                UpdateEmailCommand.RaiseCanExecuteChanged();
            }
        }
    }

    #endregion

    #region 命令

    /// <summary>
    /// 更新手机号码命令
    /// </summary>
    public DelegateCommand UpdatePhoneCommand { get; }

    /// <summary>
    /// 更新邮箱地址命令
    /// </summary>
    public DelegateCommand UpdateEmailCommand { get; }

    /// <summary>
    /// 更改密码命令
    /// </summary>
    public DelegateCommand ChangePasswordCommand { get; }

    #endregion

    #region 私有方法

    /// <summary>
    /// 加载用户数据
    /// </summary>
    private async Task LoadUserDataAsync()
    {
        try
        {
            var loginUser = await _loginSession.GetLoginUserAsync(CancellationToken.None);
            if (loginUser != null)
            {
                PhoneNumber = loginUser.PhoneNumber ?? string.Empty;
                EmailAddress = loginUser.Email ?? string.Empty;
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "加载用户数据失败");
        }
    }

    /// <summary>
    /// 更新手机号码
    /// </summary>
    private async void OnUpdatePhone()
    {
        try
        {
            await _messageBox.ShowInfoAsync("手机号码更新功能正在开发中，敬请期待！");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "更新手机号码失败");
            await _messageBox.ShowErrorAsync("更新手机号码时发生错误，请稍后重试。");
        }
    }

    /// <summary>
    /// 判断是否可以更新手机号码
    /// </summary>
    private bool CanUpdatePhone()
    {
        // 目前功能未开放，始终返回false
        return false;
    }

    /// <summary>
    /// 更新邮箱地址
    /// </summary>
    private async void OnUpdateEmail()
    {
        try
        {
            await _messageBox.ShowInfoAsync("邮箱地址更新功能正在开发中，敬请期待！");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "更新邮箱地址失败");
            await _messageBox.ShowErrorAsync("更新邮箱地址时发生错误，请稍后重试。");
        }
    }

    /// <summary>
    /// 判断是否可以更新邮箱地址
    /// </summary>
    private bool CanUpdateEmail()
    {
        // 目前功能未开放，始终返回false
        return false;
    }

    /// <summary>
    /// 更改密码
    /// </summary>
    private async void OnChangePassword()
    {
        try
        {
            await _messageBox.ShowInfoAsync("密码更改功能正在开发中，敬请期待！");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "更改密码失败");
            await _messageBox.ShowErrorAsync("更改密码时发生错误，请稍后重试。");
        }
    }

    /// <summary>
    /// 判断是否可以更改密码
    /// </summary>
    private bool CanChangePassword()
    {
        // 目前功能未开放，始终返回false
        return false;
    }

    #endregion
}
