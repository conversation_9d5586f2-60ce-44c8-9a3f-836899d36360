<UserControl x:Class="DeviceGuard.Shell.Views.Pages.MyAccountPage.MyAccountPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:myAccountPage="clr-namespace:DeviceGuard.Shell.Views.Pages.MyAccountPage"
             xmlns:prism="clr-namespace:DeviceGuard.Windows.Utility.Prism;assembly=DeviceGuard.Windows.Utility"
             xmlns:conv="clr-namespace:DeviceGuard.Windows.Controls.Conv;assembly=DeviceGuard.Windows.Controls"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             prism:ViewModelBinder.BindingView="True"
             mc:Ignorable="d"
             d:DesignHeight="600"
             d:DesignWidth="1000"
             d:DataContext="{d:DesignInstance myAccountPage:MyAccountPageViewModel}">

    <UserControl.Resources>
        <Style x:Key="SidebarItemStyle" TargetType="Border">
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Background" Value="Transparent"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#F0F0F0"/>
                </Trigger>
                <DataTrigger Binding="{Binding IsSelected}" Value="True">
                    <Setter Property="Background" Value="#E3F2FD"/>
                    <Setter Property="BorderBrush" Value="#2196F3"/>
                    <Setter Property="BorderThickness" Value="2,0,0,0"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <!-- 主容器 -->
    <materialDesign:Card Margin="20" materialDesign:ShadowAssist.ShadowDepth="Depth3">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="250"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧导航菜单 -->
            <Border Grid.Column="0" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="0,0,1,0">
                <StackPanel Margin="20,30">
                    <ItemsControl ItemsSource="{Binding MenuItems}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Border Style="{StaticResource SidebarItemStyle}">
                                    <Border.InputBindings>
                                        <MouseBinding MouseAction="LeftClick"
                                                    Command="{Binding DataContext.MenuItemClickCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                    CommandParameter="{Binding}"/>
                                    </Border.InputBindings>
                                    <StackPanel Orientation="Horizontal">
                                        <materialDesign:PackIcon Kind="{Binding Icon}"
                                                               Width="20" Height="20"
                                                               VerticalAlignment="Center"
                                                               Foreground="#666"/>
                                        <TextBlock Text="{Binding Name}"
                                                 Margin="12,0,0,0"
                                                 VerticalAlignment="Center"
                                                 FontSize="14"
                                                 Foreground="#333"/>
                                    </StackPanel>
                                </Border>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </StackPanel>
            </Border>

            <!-- 右侧内容区域 -->
            <Grid Grid.Column="1" Background="White">
                <!-- 未登录状态 -->
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                          Visibility="{Binding IsLoggedIn, Converter={StaticResource DisplayFalseElseCollapsedConverter}, Mode=OneWay}">
                    <materialDesign:PackIcon Kind="AccountCircleOutline" Width="80" Height="80"
                                           Foreground="#CCC" Margin="0,0,0,20"/>
                    <TextBlock Text="使用你的账户登录, 获取完整功能"
                             FontSize="16" Foreground="#666"
                             HorizontalAlignment="Center" Margin="0,0,0,20"/>
                    <Button Content="登录" Command="{Binding LoginCommand}"
                          Style="{StaticResource MaterialDesignRaisedButton}"
                          Width="120" Height="40"/>
                </StackPanel>

                <!-- 已登录状态 - 显示对应的子页面 -->
                <Grid Visibility="{Binding IsLoggedIn, Converter={StaticResource BooleanToVisibilityConverter}, Mode=OneWay}">
                    <!-- 个人信息页面 -->
                    <myAccountPage:PersonalInfoView Visibility="{Binding CurrentPageType, Converter={StaticResource EqualityToVisibilityConverter}, ConverterParameter=PersonalInfo}"/>

                    <!-- 其他页面占位符 -->
                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                              Visibility="{Binding CurrentPageType, Converter={StaticResource EqualityToVisibilityConverter}, ConverterParameter=SecuritySettings}">
                        <TextBlock Text="安全设置" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="此页面正在开发中..." FontSize="14" Foreground="#666" HorizontalAlignment="Center" Margin="0,10"/>
                    </StackPanel>

                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                              Visibility="{Binding CurrentPageType, Converter={StaticResource EqualityToVisibilityConverter}, ConverterParameter=DeviceManagement}">
                        <TextBlock Text="设备管理" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="此页面正在开发中..." FontSize="14" Foreground="#666" HorizontalAlignment="Center" Margin="0,10"/>
                    </StackPanel>

                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                              Visibility="{Binding CurrentPageType, Converter={StaticResource EqualityToVisibilityConverter}, ConverterParameter=NotificationSettings}">
                        <TextBlock Text="通知设置" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="此页面正在开发中..." FontSize="14" Foreground="#666" HorizontalAlignment="Center" Margin="0,10"/>
                    </StackPanel>

                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                              Visibility="{Binding CurrentPageType, Converter={StaticResource EqualityToVisibilityConverter}, ConverterParameter=AccountInfo}">
                        <TextBlock Text="账户信息" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="此页面正在开发中..." FontSize="14" Foreground="#666" HorizontalAlignment="Center" Margin="0,10"/>
                    </StackPanel>

                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                              Visibility="{Binding CurrentPageType, Converter={StaticResource EqualityToVisibilityConverter}, ConverterParameter=UsageStatistics}">
                        <TextBlock Text="使用统计" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="此页面正在开发中..." FontSize="14" Foreground="#666" HorizontalAlignment="Center" Margin="0,10"/>
                    </StackPanel>

                    <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center"
                              Visibility="{Binding CurrentPageType, Converter={StaticResource EqualityToVisibilityConverter}, ConverterParameter=HelpCenter}">
                        <TextBlock Text="帮助中心" FontSize="24" HorizontalAlignment="Center"/>
                        <TextBlock Text="此页面正在开发中..." FontSize="14" Foreground="#666" HorizontalAlignment="Center" Margin="0,10"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Grid>
    </materialDesign:Card>
</UserControl>