<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户个人信息</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .profile-container {
            width: 1000px;
            height: 600px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .sidebar {
            width: 250px;
            background: #f8f9fa;
            padding: 30px 20px;
            border-right: 1px solid #e9ecef;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            margin-bottom: 10px;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #6c757d;
            font-size: 16px;
        }

        .sidebar-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .sidebar-item.active {
            background: #667eea;
            color: white;
        }

        .sidebar-item i {
            margin-right: 15px;
            font-size: 20px;
        }

        .main-content {
            flex: 1;
            padding: 40px;
            overflow-y: auto;
        }

        .header {
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 28px;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 16px;
        }

        .profile-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
        }

        .avatar-section {
            display: flex;
            align-items: center;
            margin-bottom: 40px;
            padding-bottom: 30px;
            border-bottom: 2px solid #f8f9fa;
        }

        .avatar-container {
            position: relative;
            margin-right: 30px;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: white;
            border: 4px solid white;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }

        .avatar:hover {
            transform: scale(1.05);
        }

        .avatar-upload {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 36px;
            height: 36px;
            background: #667eea;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .avatar-upload:hover {
            background: #5a6fd8;
            transform: scale(1.1);
        }

        .avatar-info h2 {
            font-size: 24px;
            color: #2c3e50;
            margin-bottom: 8px;
        }

        .avatar-info p {
            color: #7f8c8d;
            font-size: 16px;
            margin-bottom: 15px;
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            background: #d4edda;
            color: #155724;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .status-badge::before {
            content: '';
            width: 8px;
            height: 8px;
            background: #28a745;
            border-radius: 50%;
            margin-right: 8px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
            font-size: 14px;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-control:hover {
            border-color: #ced4da;
            background: white;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 2px solid #e9ecef;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .edit-mode .form-control {
            background: white;
            border-color: #667eea;
        }

        .edit-mode .form-control[readonly] {
            background: #f8f9fa;
            border-color: #e9ecef;
        }

        /* 图标样式 */
        .icon {
            display: inline-block;
            width: 20px;
            height: 20px;
            text-align: center;
            line-height: 20px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .avatar-section {
                flex-direction: column;
                text-align: center;
            }

            .avatar-container {
                margin-right: 0;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
<div class="profile-container">
    <div class="sidebar">
        <div class="sidebar-item active">
            <span class="icon">👤</span>
            <span>个人信息</span>
        </div>
        <div class="sidebar-item">
            <span class="icon">🔐</span>
            <span>安全设置</span>
        </div>
        <div class="sidebar-item">
            <span class="icon">📱</span>
            <span>设备管理</span>
        </div>
        <div class="sidebar-item">
            <span class="icon">🔔</span>
            <span>通知设置</span>
        </div>
        <div class="sidebar-item">
            <span class="icon">💳</span>
            <span>账户信息</span>
        </div>
        <div class="sidebar-item">
            <span class="icon">📊</span>
            <span>使用统计</span>
        </div>
        <div class="sidebar-item">
            <span class="icon">❓</span>
            <span>帮助中心</span>
        </div>
    </div>

    <div class="main-content">
        <div class="header">
            <h1>个人信息</h1>
            <p>管理您的个人信息和账户设置</p>
        </div>

        <div class="profile-section">
            <div class="avatar-section">
                <div class="avatar-container">
                    <div class="avatar" id="avatar">
                        <span id="avatarText">T</span>
                    </div>
                    <div class="avatar-upload" onclick="uploadAvatar()">
                        <span>📷</span>
                    </div>
                </div>
                <div class="avatar-info">
                    <h2 id="displayName">Test User</h2>
                    <p>上次登录：2025年7月6日 14:32</p>
                    <span class="status-badge">账户正常</span>
                </div>
            </div>

            <form id="profileForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="loginName">登录名</label>
                        <input type="text" id="loginName" name="loginName" class="form-control" value="testuser" readonly>
                    </div>
                    <div class="form-group">
                        <label for="userName">用户名</label>
                        <input type="text" id="userName" name="userName" class="form-control" value="Test User" readonly>
                    </div>
                    <div class="form-group">
                        <label for="phone">手机号</label>
                        <input type="tel" id="phone" name="phone" class="form-control" value="138****8888" readonly>
                    </div>
                    <div class="form-group">
                        <label for="email">邮箱地址</label>
                        <input type="email" id="email" name="email" class="form-control" value="<EMAIL>" readonly>
                    </div>
                    <div class="form-group full-width">
                        <label for="bio">个人简介</label>
                        <textarea id="bio" name="bio" class="form-control" rows="3" readonly placeholder="介绍一下自己吧...">这是一个测试用户的个人简介。</textarea>
                    </div>
                </div>

                <div class="btn-group">
                    <button type="button" class="btn btn-secondary" onclick="cancelEdit()">
                        <span>↩️</span>
                        <span>取消</span>
                    </button>
                    <button type="button" class="btn btn-primary" id="editBtn" onclick="toggleEdit()">
                        <span>✏️</span>
                        <span>编辑</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    let isEditing = false;
    let originalData = {};

    function toggleEdit() {
        const form = document.getElementById('profileForm');
        const editBtn = document.getElementById('editBtn');

        if (!isEditing) {
            // 进入编辑模式
            isEditing = true;
            form.classList.add('edit-mode');

            // 保存原始数据
            const inputs = form.querySelectorAll('.form-control');
            inputs.forEach(input => {
                if (input.name !== 'loginName') { // 登录名不可编辑
                    originalData[input.name] = input.value;
                    input.readonly = false;
                }
            });

            editBtn.innerHTML = '<span>💾</span><span>保存</span>';
            editBtn.style.background = '#28a745';
        } else {
            // 保存并退出编辑模式
            saveProfile();
        }
    }

    function saveProfile() {
        const form = document.getElementById('profileForm');
        const editBtn = document.getElementById('editBtn');

        // 这里可以添加保存逻辑
        console.log('保存用户信息...');

        // 更新显示名称
        const userName = document.getElementById('userName').value;
        document.getElementById('displayName').textContent = userName;

        // 更新头像首字母
        const avatarText = document.getElementById('avatarText');
        avatarText.textContent = userName.charAt(0).toUpperCase();

        // 退出编辑模式
        isEditing = false;
        form.classList.remove('edit-mode');

        const inputs = form.querySelectorAll('.form-control');
        inputs.forEach(input => {
            if (input.name !== 'loginName') {
                input.readonly = true;
            }
        });

        editBtn.innerHTML = '<span>✏️</span><span>编辑</span>';
        editBtn.style.background = '#667eea';

        // 显示保存成功提示
        showNotification('个人信息已更新', 'success');
    }

    function cancelEdit() {
        if (!isEditing) return;

        const form = document.getElementById('profileForm');
        const editBtn = document.getElementById('editBtn');

        // 恢复原始数据
        Object.keys(originalData).forEach(key => {
            const input = form.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = originalData[key];
            }
        });

        // 退出编辑模式
        isEditing = false;
        form.classList.remove('edit-mode');

        const inputs = form.querySelectorAll('.form-control');
        inputs.forEach(input => {
            if (input.name !== 'loginName') {
                input.readonly = true;
            }
        });

        editBtn.innerHTML = '<span>✏️</span><span>编辑</span>';
        editBtn.style.background = '#667eea';

        originalData = {};
    }

    function uploadAvatar() {
        // 模拟头像上传
        const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe'];
        const randomColor = colors[Math.floor(Math.random() * colors.length)];

        const avatar = document.getElementById('avatar');
        avatar.style.background = `linear-gradient(45deg, ${randomColor}, ${colors[(colors.indexOf(randomColor) + 1) % colors.length]})`;

        showNotification('头像已更新', 'success');
    }

    function showNotification(message, type) {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 25px;
                background: ${type === 'success' ? '#d4edda' : '#f8d7da'};
                color: ${type === 'success' ? '#155724' : '#721c24'};
                border-radius: 10px;
                border: 1px solid ${type === 'success' ? '#c3e6cb' : '#f5c6cb'};
                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                z-index: 1000;
                font-weight: 500;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;
        notification.textContent = message;

        document.body.appendChild(notification);

        // 动画显示
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // 自动消失
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    // 侧边栏交互
    document.querySelectorAll('.sidebar-item').forEach(item => {
        item.addEventListener('click', function() {
            document.querySelectorAll('.sidebar-item').forEach(i => i.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // 表单验证
    document.getElementById('phone').addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 11) value = value.slice(0, 11);
        if (value.length > 3 && value.length <= 7) {
            value = value.replace(/(\d{3})(\d+)/, '$1****$2');
        } else if (value.length > 7) {
            value = value.replace(/(\d{3})(\d{4})(\d+)/, '$1****$3');
        }
        e.target.value = value;
    });

    document.getElementById('email').addEventListener('blur', function(e) {
        const email = e.target.value;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (email && !emailRegex.test(email)) {
            e.target.style.borderColor = '#dc3545';
            showNotification('请输入有效的邮箱地址', 'error');
        } else {
            e.target.style.borderColor = '#667eea';
        }
    });
</script>
</body>
</html>