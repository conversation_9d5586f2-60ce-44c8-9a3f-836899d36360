using System.Globalization;
using System.IO;
using System.Reflection;
using System.Windows;
using CefSharp;
using CefSharp.Wpf;
using DeviceGuard.Shell.Startup;
using DeviceGuard.Shell.Views.Home;
using DeviceGuard.Shell.Views.Main;
using DeviceGuard.Shell.Views.PageNotFound;
using MaterialDesignThemes.Wpf;
using Serilog;
using Serilog.Events;
using DeviceGuard.Infrastructure.DependencyInjection;
using DeviceGuard.Infrastructure.FileSystem;
using DeviceGuard.Interface.FileSystem;
using DeviceGuard.Interface.Licenses;
using DeviceGuard.Interface.Prism;
using DeviceGuard.ModuleCore.DependencyInjection;
using DeviceGuard.Shell.Interface.Breadcrumbs;
using DeviceGuard.Shell.Interface.Breadcrumbs.Impl;
using DeviceGuard.Shell.Interface.Pages;
using DeviceGuard.Shell.Licenses;
using DeviceGuard.Shell.ReleaseReader.Implements;
using DeviceGuard.Shell.Views.CategoryDetails;
using DeviceGuard.Shell.Views.CategoryList;
using DeviceGuard.Shell.Views.LoginDialog;
using DeviceGuard.Shell.Views.Pages.CategoryPage;
using DeviceGuard.Shell.Views.Pages.HistoryPage;
using DeviceGuard.Shell.Views.Pages.MyAccountPage;
using DeviceGuard.Shell.Views.Pages.PluginPage;
using DeviceGuard.Shell.Views.Pages.RecentPage;
using DeviceGuard.Shell.Views.Pages.SearchPage;
using DeviceGuard.Shell.Views.Pages.SettingsPage;
using DeviceGuard.Shell.Views.PluginDetails;
using DeviceGuard.Shell.Security;
using DeviceGuard.Windows.Infrastructure.DependencyInjection;
using DeviceGuard.Windows.Interface.ViewLoader;
using DeviceGuard.Windows.Utility;
using DeviceGuard.Windows.Utility.Dialogs;
using DeviceGuardCloud.WebApi.ReleaseReader;


namespace DeviceGuard.Shell;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App
{
    private readonly ApplicationUnhandledException _unhandledException         = new();
    private readonly MultipleInstancePrevention    _multipleInstancePrevention = new();

    // /// <summary>
    // /// 运行核心服务的任务
    // /// </summary>
    // private Task? _serviceTask;

    protected override void OnStartup(StartupEventArgs e)
    {
#if DEBUG
        // 调试模式, 默认使用程序目录
        AppFolderService.BaseFolder = "./UserData";
#endif

        var culture = new CultureInfo("zh-CN");
        CultureInfo.CurrentCulture            = culture;
        CultureInfo.CurrentUICulture          = culture;
        Thread.CurrentThread.CurrentCulture   = culture;
        Thread.CurrentThread.CurrentUICulture = culture;

        // 配置日志系统
        var baseFolder = new AppFolderService();
        var path       = baseFolder.GetOrCreateFolder(AppFolder.Logs);

        Log.Logger = new LoggerConfiguration()
                     .WriteTo.File($"{path}\\log-.txt", rollingInterval: RollingInterval.Day, restrictedToMinimumLevel: LogEventLevel.Debug)
                     .CreateLogger();

        Log.Verbose("App starting...");

        var settings = new CefSettings
        {
            LogSeverity = LogSeverity.Info
        };
        settings.CefCommandLineArgs.Add("disable-gpu",             "1"); // 解决黑屏问题
        settings.CefCommandLineArgs.Add("disable-gpu-compositing", "1");
        settings.SetOffScreenRenderingBestPerformanceArgs();
        settings.MultiThreadedMessageLoop   = true;
        settings.WindowlessRenderingEnabled = true;

        // 让 WPF 能够兼容 WindowsFormsHost
        Cef.Initialize(settings);

        // 全局异常订阅处理
        _unhandledException.SubscribeUnhandledExceptions();

        // 防止软件重入
        if (!_multipleInstancePrevention.Initialize(Path.Combine(AppDomain.CurrentDomain.FriendlyName) + "{24706BAC-F14C-4311-894C-5542AFFC1DDB}"))
        {
            Shutdown();
            return;
        }

        // 启动 Prism 框架
        base.OnStartup(e);

        Log.Verbose("App started.");

        // 订阅其他进程防止多开的唤醒事件
        _multipleInstancePrevention.WakeUpEventReceived += () =>
        {
            Current.Dispatcher.BeginInvoke(new Action(() =>
            {
                var win = Current.MainWindow;
                if (win != null)
                {
                    win.Show();
                    win.Focus();
                    win.Activate();
                }
            }));
        };

        // Container 已经工作, 这里设置 MessageBox 给异常处理模块, 让他可以显示信息
        var messageBox = Container.Resolve<IMessageBox>();
        _unhandledException.SetMessageBox(messageBox);

        // 软件框架准备完毕, 开始启动核心服务
        // _serviceTask = Task.Run(async () =>
        // {
        //     // TODO 启动系统
        //     // _container = Container.Resolve<INcContainer>();
        //     // await _container.InitializeAsync();
        // });
    }

    protected override IContainerExtension CreateContainerExtension()
    {
        // add resolve diagnostic information when error
        return new UnityContainerExtension(new UnityContainer().AddExtension(new Diagnostic()));
    }

    protected override void ConfigureRegionAdapterMappings(RegionAdapterMappings regionAdapterMappings)
    {
        // TODO 临时关闭
        // 配置 RegionManager 放置于 TabControl 和 TabablzControl 的效果
        // regionAdapterMappings.RegisterMapping<TabControl, TabControlRegionAdapter>();
        // regionAdapterMappings.RegisterMapping<TabablzControl, TabablzControlRegionAdapter>();

        base.ConfigureRegionAdapterMappings(regionAdapterMappings);
    }

    /// <summary>
    /// 注册启动必要的模块
    /// </summary>
    /// <param name="containerRegistry"></param>
    protected override void RegisterTypes(IContainerRegistry containerRegistry)
    {
        // register logging
        containerRegistry.RegisterInstance(Log.Logger);

        // Main window
        containerRegistry.GetContainer().RegisterSingleton<MainWindowViewModel>();                                 // serilog output
        containerRegistry.GetContainer().RegisterFactory<IMainWindowTitle>(c => c.Resolve<MainWindowViewModel>()); // serilog output

        // messages
        containerRegistry.RegisterSingleton<ISnackbarMessageQueue, SnackbarMessageQueue>();
        containerRegistry.RegisterSingleton<IBreadcrumb, Breadcrumb>();

        // views
        containerRegistry.RegisterForNavigation<HomeView>();
        containerRegistry.RegisterForNavigation<BlankView>();
        containerRegistry.RegisterForNavigation<PluginDetailsView>();
        // containerRegistry.RegisterInstance(_outputViewModel); // serilog output

        // -- Pages ---------------------------------------------------------
        // 首页
        //containerRegistry.RegisterForNavigation<IndexPageView>();
        //containerRegistry.RegisterSingleton<IPageViewLoader, IndexPageViewLoader>(nameof(IndexPageViewLoader));
        // 最近访问
        containerRegistry.RegisterForNavigation<RecentPageView>();
        containerRegistry.RegisterSingleton<IPrimaryButton, RecentPageViewLoader>(nameof(RecentPageViewLoader));
        // 搜搜
        containerRegistry.RegisterForNavigation<SearchPageView>();
        containerRegistry.RegisterForNavigation<SearchResultPageView>();
        containerRegistry.RegisterSingleton<IPrimaryButton, SearchPageViewLoader>(nameof(SearchPageViewLoader));
        // 分类
        containerRegistry.RegisterForNavigation<CategoryPageView>();
        containerRegistry.RegisterSingleton<IPrimaryButton, CategoryPageViewLoader>(nameof(CategoryPageViewLoader));
        // 历史记录
        containerRegistry.RegisterForNavigation<HistoryPageView>();
        containerRegistry.RegisterSingleton<IPrimaryButton, HistoryPageViewLoader>(nameof(HistoryPageViewLoader));
        // 我的账户
        containerRegistry.RegisterForNavigation<MyAccountPageView>();
        containerRegistry.RegisterSingleton<IPrimaryButton, MyAccountPageViewLoader>(nameof(MyAccountPageViewLoader));
        // 设置
        containerRegistry.RegisterForNavigation<SettingsPageView>();
        containerRegistry.RegisterSingleton<IPrimaryButton, SettingsPageViewLoader>(nameof(SettingsPageViewLoader));

        // 插件视图加载器
        containerRegistry.RegisterSingleton<IPluginPageViewLoaderFactory, PluginPageViewLoaderFactory>();

        containerRegistry.RegisterForNavigation<CategoryDetailsView>(); // 查询插件分类详情
        containerRegistry.RegisterForNavigation<CategoryListView>();    // 查询插件分类的子分类信息页
        containerRegistry.RegisterForNavigation<PluginDetailsView>();   // 查询插件详情

        // 登录
        containerRegistry.Register<LoginDialogView>();
        containerRegistry.Register<ILoginDialog, LoginDialog>();

        // Others
        containerRegistry.RegisterSingleton<IPcSerialNumber, PcSerialNumber>();
        containerRegistry.Register<IChangelogReader, ChangelogReader>();

        // load components
        containerRegistry.RegisterComponent<InfrastructureComponent>();
        containerRegistry.RegisterComponent<ModulesComponent>();
        containerRegistry.RegisterComponent<WindowsInfrastructureComponent>();
        containerRegistry.RegisterComponent<SecurityComponent>();

        //
        // containerRegistry.GetContainer().RegisterComponent<NcServiceComponent>();
        //
        // // 连接的设备
        // containerRegistry.GetContainer().RegisterComponent<NcCommGrblComponent>();             // 标准 Grbl
        // containerRegistry.GetContainer().RegisterComponent<NcCommExGrblComponent>();           // Octopus ExGrbl
        // containerRegistry.GetContainer().RegisterComponent<NcCommExGrblWpfSettingComponent>(); // Octopus ExGrbl Setting
        //
        // // 通讯方式
        // containerRegistry.GetContainer().RegisterComponent<NcSerialTransmitComponent>();        // Serial port
        // containerRegistry.GetContainer().RegisterComponent<NcTcpTransmitComponent>();           // Tcp
        // containerRegistry.GetContainer().RegisterComponent<NcTcpTransmitWpfSettingComponent>(); // Tcp
        //
        // containerRegistry.GetContainer().RegisterComponent<GCodeInterpreterComponent>();
        // containerRegistry.GetContainer().RegisterComponent<FileProviderComponent>();
        //
        // containerRegistry.GetContainer().RegisterComponent<ComputerVisionComponent>();
        //
        // // 飞针测试组件
        // containerRegistry.GetContainer().RegisterComponent<FlyingProbeTesterLogicalityComponent>();           // Flying probe component for logical
        // containerRegistry.GetContainer().RegisterComponent<FlyingProbeTesterExGrblComponent>();               // Flying probe component for exGrbl
        // containerRegistry.GetContainer().RegisterComponent<FlyingProbeReportBasedOnExcelTemplateComponent>(); // Flying probe component for exGrbl
    }

    /// <summary>
    /// 注册可后期加载的模块, 这些模块在 MainWindow 初始化的时候都还没有加载
    /// 这里注册的模块主要是UI模块.
    /// </summary>
    /// <param name="moduleCatalog"></param>
    protected override void ConfigureModuleCatalog(IModuleCatalog moduleCatalog)
    {
        // TODO 目前动态加载模块, 但是模块自己引用的 System.IO.Ports 无法被加载, 提示 System.IO.Ports is currently only supported on Windows
        // TODO 检查并解决该问题, 然后从主项目中移除对 System.IO.Ports 的加载
    }


    /// <summary>
    /// 创建 Shell
    /// </summary>
    /// <returns></returns>
    protected override Window CreateShell()
    {
        // 首次在UI解析 EventAggregator
        Container.Resolve<IEventAggregator>();
        var view = Container.Resolve<MainWindow>();

        return view;
    }

    protected override void ConfigureViewModelLocator()
    {
        base.ConfigureViewModelLocator();

        // todo 有时间重写 Prism 的方法, 静态方法无法 override 导致每次窗口都要手动 BindingLoadedUnloaded
        ViewModelLocationProvider.SetDefaultViewTypeToViewModelTypeResolver(viewType =>
        {
            var viewName = viewType.FullName;
            if (viewName == null)
                throw new ArgumentNullException(nameof(viewName));

            // same folder but end with ViewModel
            var suffix        = viewName.EndsWith("View") ? "Model" : "ViewModel";
            var viewModelName = string.Format(CultureInfo.InvariantCulture, "{0}{1}", viewName, suffix);

            var assembly = viewType.GetTypeInfo().Assembly;
            var type     = assembly.GetType(viewModelName, true);

            return type;
        });
    }


    protected override void OnExit(ExitEventArgs e)
    {
        // TODO 进程结束, 关闭容器
        // if (_container != null)
        //     await Task.Run(() => _container.CleanupAsync());

        _multipleInstancePrevention.Dispose();
        Cef.Shutdown();
        base.OnExit(e);
    }
}
