using Doulex.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;

namespace AspNetCore.Infrastructure.Security;

public static class KeycloakAuthenticationDependencyInjection
{
    public static IServiceCollection AddAuthenticationUsingKeycloak(
        this IServiceCollection               services,
        Action<KeycloakAuthenticationOptions> optionConfigure)
    {
        if (services == null) throw new ArgumentNullException(nameof(services));
        if (optionConfigure == null) throw new ArgumentNullException(nameof(optionConfigure));

        services.Configure(optionConfigure);

        var options = new KeycloakAuthenticationOptions();
        optionConfigure(options);

        services.AddAuthenticationUsingKeycloak(options);
        return services;
    }

    public static IServiceCollection AddAuthenticationUsingKeycloak(this IServiceCollection services, IConfiguration configuration)
    {
        if (services == null) throw new ArgumentNullException(nameof(services));
        if (configuration == null) throw new ArgumentNullException(nameof(configuration));

        services.Configure<KeycloakAuthenticationOptions>(configuration);

        var config = configuration.Get<KeycloakAuthenticationOptions>() ?? throw new Exception("KeycloakAuthenticationOptions required");
        services.AddAuthenticationUsingKeycloak(config);
        return services;
    }

    private static void AddAuthenticationUsingKeycloak(this IServiceCollection services,
        KeycloakAuthenticationOptions                                          authenticationOptions) // 可以考虑改名
    {
        if (services == null) throw new ArgumentNullException(nameof(services));
        if (authenticationOptions == null) throw new ArgumentNullException(nameof(authenticationOptions));

        services.AddAuthenticationService();

        // 认证接口
        services.AddTransient<IUserTokenService, UserTokenService>();

        services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme)
                .AddJwtBearer(options =>
                    {
                        options.Authority            = authenticationOptions.Url; // Keycloak 的 realm 地址
                        options.RequireHttpsMetadata = authenticationOptions.RequireHttpsMetadata;
                        options.Audience             = authenticationOptions.ClientId; // 这个值必须在 Keycloak 的 client 配置中设置为 Access Token 的 audience

                        // token 来源
                        options.TokenValidationParameters = new TokenValidationParameters
                        {
                            ValidateAudience = true, // 根据实际需求开启
                            ValidAudience    = authenticationOptions.ClientId,
                            ValidateIssuer   = true,
                            // ValidIssuer      = authenticationOptions.OAuthServerUrl?.TrimEnd('/'), // 注意确保没有重复斜杠 TODO 如何弄?
                            ValidateLifetime         = true,
                            ValidateIssuerSigningKey = true,
                        };

                        // 自定义 Token 获取逻辑：从 header 和 query string 中都支持
                        options.Events = new JwtBearerEvents
                        {
                            OnMessageReceived = context =>
                            {
                                var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
                                if (string.IsNullOrEmpty(token))
                                {
                                    token = context.Request.Query["access_token"];
                                }

                                context.Token = token;
                                return Task.CompletedTask;
                            }
                        };
                    }
                );
    }
}
