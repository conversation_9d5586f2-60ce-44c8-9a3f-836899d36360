namespace AspNetCore.Infrastructure.Security;

/// <summary>
/// 认证服务器配置参数
/// </summary>
public class KeycloakAuthenticationOptions
{
    /// <summary>
    /// OAuth 认证端 url
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// 认证服务器需要HTTPS
    /// </summary>
    public bool RequireHttpsMetadata => string.Equals(new UriBuilder(Url ?? "").Scheme, "https", StringComparison.OrdinalIgnoreCase);

    /// <summary>
    /// API 资源名称
    /// </summary>
    public string? ClientId { get; set; }

    /// <summary>
    /// API 资源密码
    /// </summary>
    public string? ClientSecret { get; set; }
    //
    // /// <summary>
    // /// 启用内省缓存
    // /// </summary>
    // public bool EnableIntrospectionCache { get; set; } = true;
    //
    // /// <summary>
    // /// 内省缓存有效期
    // /// </summary>
    // public TimeSpan IntrospectionCacheDuration { get; set; } = new TimeSpan(0, 10, 0);
    
    /// <summary>
    /// 系统管理员的角色名
    /// </summary>
    public string? SuperUserRoleName { get; set; }
    
    /// <summary>
    /// 颁发者 (收到的TOKEN中的iss字段要与此处一致)
    /// </summary>
    public string? Issuer { get; set; }
}
