using AspNetCore.Infrastructure.Controller.Filters;
using AspNetCore.Infrastructure.ExceptionResponse;
using Doulex.AspNetCore.Conventions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;

namespace AspNetCore.Infrastructure.Controller;

public static class ControllersDependencyInjection
{
    public static IServiceCollection AddCustomControllers(this IServiceCollection services)
    {
        services.AddControllers(options =>
        {
            options.Filters.Add(typeof(ValidateModelFilter));

            options.Conventions.Add(new PrefixedRouteConvention("api")); // Add a prefix to all routes
            options.Conventions.Add(new KebabControllerConvention());    // Controller name convent to kebab-case
        }).AddNewtonsoftJson(options =>
        {
            // System.Text.Json 无法处理子对象的 "get only" 的属性设置!!!, 小心使用 System.Text.Json
            options.UseMemberCasing(); // options.UseCamelCasing(false); // casing of propertyName in body of HTTP POST
            options.SerializerSettings.DefaultValueHandling = DefaultValueHandling.Include;
            options.SerializerSettings.ContractResolver     = new CamelCasePropertyNamesContractResolver();

            // 添加自定义的 Converter， 包括枚举转换，对象类型转换等
            options.SerializerSettings.Converters.Add(new StringEnumConverter(new CamelCaseNamingStrategy()));
            // options.SerializerSettings.Converters.Add(new TbEntityFilterTypeBasedConverter());
            // options.SerializerSettings.Converters.Add(new TbKeyFilterPredicateTypeBasedConverter());
        });

        services.AddEndpointsApiExplorer();

        // 这个类用来把所有的 API 方法添加全局的 ProduceResponseTypeAttribute 属性, 注意, 如果没有这个类, 则不会添加 ProduceResponseTypeAttribute 属性
        services.TryAddEnumerable(ServiceDescriptor.Transient<IApplicationModelProvider, ProduceResponseTypeModelProvider>());

        return services;
    }
}

/// <summary>
/// 全局 Controller 方法注释
/// </summary>
public class ProduceResponseTypeModelProvider : IApplicationModelProvider
{
    public int Order => 3;

    public void OnProvidersExecuted(ApplicationModelProviderContext context)
    {
    }

    public void OnProvidersExecuting(ApplicationModelProviderContext context)
    {
        foreach (var controller in context.Result.Controllers)
        {
            foreach (var action in controller.Actions)
            {
                // 配置系统的返回结果的注解
                action.Filters.Add(
                    new ProducesResponseTypeAttribute(StatusCodes.Status200OK)); // 配置默认的 200, 不要设置返回类型, 系统会自动生成

                // 配置 CustomException 的返回结果的注解
                action.Filters.Add(new ProducesResponseTypeAttribute(typeof(ErrorMessageModel),
                    StatusCodes.Status400BadRequest));
                action.Filters.Add(new ProducesResponseTypeAttribute(typeof(ErrorMessageModel),
                    StatusCodes.Status404NotFound));

                // 如果action配置了AuthorizeAttribute，则配置ProducesResponseTypeAttribute 401 403
                if (action.Attributes.Any(x => x is IAuthorizeData))
                {
                    action.Filters.Add(new ProducesResponseTypeAttribute(typeof(ErrorMessageModel),
                        StatusCodes.Status403Forbidden));
                }
            }
        }
    }
}
