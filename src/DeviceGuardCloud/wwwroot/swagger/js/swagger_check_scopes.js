function checkScopes(container) {
    const inputNodes = container.querySelectorAll(".scopes input");
    for (const checkbox of inputNodes) {
        checkbox.click();
        console.log('Scope checkbox modified: ' + checkbox.id);
    }
}

function watchDOM() {
    // target element that we will observe
    const target = document.body;

    // subscriber function
    function subscriber(mutations) {
        mutations.forEach((mutation) => {
            if (mutation.target.className === 'auth-wrapper') {
                checkScopes(mutation.target);
            }
        });
    }

    // instantiating observer
    const observer = new MutationObserver(subscriber);

    // observing target
    observer.observe(target, {
        childList: true,
        subtree: true
    });
}

document.addEventListener('DOMContentLoaded', watchDOM);
