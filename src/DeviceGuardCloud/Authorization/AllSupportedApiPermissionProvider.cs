using DeviceGuardCloud.Application.Security.Queries;
using Scru<PERSON>;

namespace DeviceGuardCloud.WebApi.Authorization;

/// <summary>
/// 系统权限提供者
/// </summary>
[ServiceDescriptor]
public class AllSupportedApiPermissionProvider : IApiPermissionProvider
{
    private static HashSet<string>? _adminPermissions;

    /// <summary>
    /// 获取所有的权限信息
    /// </summary>
    /// <returns></returns>
    public HashSet<string> GetAllSupportedAdminPermissions()
    {
        return _adminPermissions ??= [..Enum.GetNames(typeof(AdminPermission))];
    }

}
