namespace DeviceGuardCloud.WebApi.Authorization;

/// <summary>
/// 运维端用户资源访问权限
/// </summary>
public enum AdminPermission
{
    /// <summary>
    /// 读取系统审计日志
    /// </summary>
    AuditRead,

    /// <summary>
    /// 用户管理
    /// </summary>
    UserRead,
    UserUpdate,
    UserDelete,

    /// <summary>
    /// 许可证
    /// </summary>
    BoardRead,
    BoardCreate,
    BoardDelete,

    /// <summary>
    /// 系统管理员
    /// </summary>
    AdminUserCreate,
    AdminUserRead,
    AdminUserUpdate,
    AdminUserDelete,

    /// <summary>
    /// 系统角色
    /// </summary>
    AdminRoleCreate,
    AdminRoleRead,
    AdminRoleUpdate,
    AdminRoleDelete,

    /// <summary>
    /// 仪表盘
    /// </summary>
    DashboardCreate,
    DashboardRead,
    DashboardUpdate,
    DashboardDelete,

    /// <summary>
    /// 插件
    /// </summary>
    PluginCreate, // 创建插件
    PluginUpload, // 上传模块
    PluginUpdate, // 更新模块状态
    PluginDelete, // 删除模块
    PluginRead,   // 读取系统里所有的模块的信息

    /// <summary>
    /// 插件分类
    /// </summary>
    PluginCategoryRead,
    PluginCategoryCreate,
    PluginCategoryUpdate,
    PluginCategoryDelete,

    /// <summary>
    /// 用户插件
    /// </summary>
    UserPluginRead,
    UserPluginDelete,
    UserPluginUpdate,
    UserPluginCreate,

    /// <summary>
    /// 软件制品
    /// </summary>
    ArtifactRead,
    ArtifactCreate,
    ArtifactUpdate,
    ArtifactDelete,

    /// <summary>
    /// 软件制品包
    /// </summary>
    ReleaseRead,
    ReleaseDelete,
    ReleaseUpload,
    ReleaseUpdate,

    HomePageRedirection,

    ModuleUpload,
    ModuleCreate,
    ModuleDelete,
    ModuleRead,

    /// <summary>
    /// 证书管理
    /// </summary>
    CertsUpdate,
}
