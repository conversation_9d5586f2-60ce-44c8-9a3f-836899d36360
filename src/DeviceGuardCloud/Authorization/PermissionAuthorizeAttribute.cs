using Doulex.AspNetCore.Authorization.PermissionBasedAuthorization;
using Microsoft.AspNetCore.Authorization;

namespace DeviceGuardCloud.WebApi.Authorization;

/// <summary>
/// Certification Policy - Operator
/// </summary>
public class PermissionAuthorizeAttribute : PermissionBasedAuthorizeAttribute
{
    /// <summary>
    /// 构造函数, 设置要求的权限
    /// </summary>
    /// <param name="permissions">Supports AdminPermission or CustomerPermission</param>
    public PermissionAuthorizeAttribute(params object[] permissions)
    {
        var ps = from p in permissions
                 select p switch
                 {
                     AdminPermission ap => new PermissionDeclared(PermissionKind.Admin,    ap.ToString()),
                     _                  => throw new ArgumentException($"不支持的权限类型: {p.GetType().FullName}")
                 };

        PermissionRequirement = new PermissionRequirement(ps.ToArray());
    }

    /// <summary>
    /// 要求的权限
    /// </summary>
    public PermissionRequirement PermissionRequirement { get; set; }

    /// <summary>
    /// 参数模型, 该模型将被传送到策略处理函数, 因此, 把所有需要的参数构建为模型对象返回
    /// </summary>
    protected override IAuthorizationRequirement Requirement => PermissionRequirement;

    /// <summary>
    /// 授权的描述信息 (For Dev)
    /// </summary>
    public override string Description => PermissionRequirement.ToString() ?? "";

    /// <summary>Returns a string that represents the current object.</summary>
    /// <returns>A string that represents the current object.</returns>
    public override string ToString()
    {
        return Description;
    }
}
