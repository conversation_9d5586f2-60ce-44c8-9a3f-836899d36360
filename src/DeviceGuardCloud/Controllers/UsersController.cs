using System.ComponentModel.DataAnnotations;
using DeviceGuardCloud.Application.Security.Queries;
using DeviceGuardCloud.Application.Users.Commands;
using DeviceGuardCloud.Application.Users.Queries;
using DeviceGuardCloud.WebApi.Authorization;
using LinqAsync.Pages;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace DeviceGuardCloud.WebApi.Controllers;

/// <summary>
/// 所有登录系统的用户信息管理
/// </summary>
[Route("v1/[controller]")]
public class UsersController : ApiControllerBase
{
    private readonly IUserQuery      _userQuery;
    private readonly IMediator       _mediator;
    private readonly ILoginUserQuery _login;

    public UsersController(
        IUserQuery      userQuery,
        IMediator       mediator,
        ILoginUserQuery login)
    {
        _userQuery = userQuery;
        _mediator  = mediator; 
        _login     = login;
    }


    /// <summary>
    /// 获取登录用户的信息
    /// </summary>
    /// <param name="cancel">异步操作取消句柄</param>
    /// <returns></returns>
    [HttpGet("login")]
    [Authorize]
    public async Task<IActionResult> GetLoginUserAsync(CancellationToken cancel)
    {
        var user = await _login.GetLoginUserAsync(cancel);
        if (user == null) throw new ValidationException($"无效用户");
        var q = await _userQuery.GetUserAsync(user.UserId, cancel);
        return Ok(q);
    }

    /// <summary>
    /// 用户重新绑定
    /// </summary>
    /// <param name="cancel">异步操作取消句柄</param>
    [HttpPut("rebinding")]
    [Authorize]
    public async Task UpdateLoginUserBindingAsync(CancellationToken cancel)
    {
        await _mediator.Send(new UpdateLoginUserBindingCommand());
    }

    /// <summary>
    /// 用户修改昵称
    /// </summary>
    /// <param name="screenName">要修改的用户的昵称</param>
    /// <returns></returns>
    [HttpPut("screenName")]
    [Authorize]
    public async Task UpdateUserScreenNameAsync(string screenName)
    {
        var user = await _login.GetLoginUserAsync(CancellationToken.None);
        if (user == null) throw new ValidationException($"无效用户");
        UpdateUserCommand command = new UpdateUserCommand
        {
            Id         = user.UserId,
            ScreenName = screenName
        };
        await _mediator.Send(command);
    }

    /// <summary>
    /// 获取指定条件的 运维用户
    /// </summary>
    /// <param name="query">查询对象</param>
    /// <param name="cancel">异步操作取消句柄</param>
    /// <returns>返回含有翻页的查询结果</returns>
    [HttpGet]
    [ProducesResponseType(typeof(Page<UserViewModel>), 200)]
    [PermissionAuthorize(AdminPermission.UserRead)]
    public async Task<IActionResult> GetUsersAsync([FromQuery] UserQueryPredicate query, CancellationToken cancel)
    {
        var q = await _userQuery.GetUsersAsync(query, cancel);
        return Ok(q);
    }

    [HttpGet("{userId}")]
    [PermissionAuthorize(AdminPermission.UserRead)]
    public async Task<IActionResult> GetUserAsync(Guid userId, CancellationToken cancel)
    {
        var q = await _userQuery.GetUserAsync(userId, cancel);
        return Ok(q);
    }


    /// <summary>
    /// 删除指定Id的用户
    /// </summary>
    /// <param name="userId">用户Id</param>
    /// <param name="cancel">异步操作取消句柄</param>
    [HttpDelete("{userId}")]
    [PermissionAuthorize(AdminPermission.UserDelete)]
    public async Task DeleteUserAsync([FromRoute] Guid userId, CancellationToken cancel)
    {
        await _mediator.Send(new RemoveUserCommand() { Id = userId }, cancel);
    }


    /// <summary>
    /// 更新用户启动状态
    /// </summary>
    /// <param name="userId">要修改的用户的Id</param>
    /// <param name="command">用户更新命令</param>
    /// <returns></returns>
    [HttpPut("{userId}")]
    [PermissionAuthorize(AdminPermission.UserUpdate)]
    public async Task UpdateUserAsync(Guid userId, [FromBody] UpdateUserCommand command)
    {
        command.Id = userId;
        await _mediator.Send(command);
    }
}
