using AspNetCore.Abstractions.Exceptions;
using DeviceGuardCloud.Application.AdminUsers.Commands;
using DeviceGuardCloud.Application.AdminUsers.Queries;
using DeviceGuardCloud.WebApi.Authorization;
using DeviceGuardCloud.WebApi.Models;
using LinqAsync.Pages;
using MediatR;
using Microsoft.AspNetCore.Mvc;

namespace DeviceGuardCloud.WebApi.Controllers;

/// <summary>
/// 平台运维管理员相关的API
/// </summary>
[Route("v1/[controller]")]
public class AdminUsersController : ApiControllerBase
{
    private readonly IAdminUserQuery _adminUserQuery;
    private readonly IMediator       _mediator;

    public AdminUsersController(IMediator mediator, IAdminUserQuery adminUserQuery)
    {
        _mediator       = mediator;
        _adminUserQuery = adminUserQuery;
    }

    /// <summary>
    /// 获取平台运维管理员列表
    /// </summary>
    /// <returns></returns>
    [HttpGet()]
    [PermissionAuthorize(AdminPermission.AdminUserRead)]
    public async Task<Page<AdminUserViewModel>> GetAdminUsersAsync([FromQuery] AdminUserQueryPredicate query, CancellationToken cancel)
    {
        var result = await _adminUserQuery.GetAdminUsersAsync(query, cancel);
        return result;
    }

    /// <summary>
    /// 根据ID获取平台运维管理员
    /// </summary>
    /// <param name="adminUserId">要查询的管理员的对象Id</param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    [HttpGet("{adminUserId}")]
    [PermissionAuthorize(AdminPermission.AdminUserRead)]
    public async Task<AdminUserViewModel> GetAdminUserAsync(Guid adminUserId, CancellationToken cancel)
    {
        var adminUser = await _adminUserQuery.GetAdminUserAsync(adminUserId, cancel) ?? throw new NotFoundException("管理员不存在");
        return adminUser;
    }

    /// <summary>
    /// 添加平台运维管理员
    /// </summary>
    /// <param name="createCommand"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    [HttpPost]
    [PermissionAuthorize(AdminPermission.AdminUserCreate)]
    public async Task<IdentModel> CreateAdminUserAsync([FromBody] CreateAdminUserCommand createCommand, CancellationToken cancel)
    {
        var newUserId = await _mediator.Send(createCommand, cancel);
        return new IdentModel(newUserId);
    }

    /// <summary>
    /// 更新平台运维管理员
    /// </summary>
    /// <param name="adminUserId">要修改的管理员用户的Id</param>
    /// <param name="command">管理员更新命令</param>
    /// <returns></returns>
    [HttpPut("{adminUserId}")]
    [PermissionAuthorize(AdminPermission.UserUpdate)]
    public async Task UpdateAdminUserAsync(Guid adminUserId, [FromBody] UpdateAdminUserCommand command)
    {
        command.Id = adminUserId;
        await _mediator.Send(command);
    }

    /// <summary>
    /// 删除登录的管理账户
    /// </summary>
    /// <param name="adminUserId"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    [HttpDelete("{adminUserId}")]
    [PermissionAuthorize(AdminPermission.AdminUserDelete)]
    public async Task DeleteAdminUserAsync(Guid adminUserId, CancellationToken cancel = default)
    {
        var command = new RemoveAdminUserCommand() { Id = adminUserId };
        await _mediator.Send(command, cancel);
    }
}
