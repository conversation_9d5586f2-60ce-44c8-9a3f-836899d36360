using AspNetCore.Infrastructure.Controller;
using AspNetCore.Infrastructure.Cors;
using AspNetCore.Infrastructure.ExceptionResponse;
using AspNetCore.Infrastructure.Mapster;
using AspNetCore.Infrastructure.Security;
using AspNetCore.Infrastructure.Swagger;
using Destructurama;
using DotNetCore.Infrastructure.Caching;
using DotNetCore.Infrastructure.Scrutor;
using Doulex.AspNetCore.ExceptionResponse;
using Doulex.AspNetCore.SerilogExtensions;
using Doulex.DotNetCore;
using Doulex.AspNetCore.Authorization.PermissionBasedAuthorization;
using DeviceGuardCloud.Application.DependencyInjection;
using DeviceGuardCloud.Application.Queries.DependencyInjection;
using DeviceGuardCloud.DomainModel.Persistence.SqlServer.DependencyInjection;
using DeviceGuardCloud.Infrastructure.Mediatr;
using DeviceGuardCloud.WebApi.Authorization;
using DeviceGuardCloud.WebApi.Middlewares;
using DeviceGuardCloud.WebApi.Options;
using Serilog;
using DeviceGuardCloud.Application.DashboardRules;
using DeviceGuardCloud.Application.Signatures;
using WeChatApi.Service.DependencyInjection;

////////////////////////////////////////////////////////////////////////////////////////////////
// 1. Create the service builder and service injection
var builder = WebApplication.CreateBuilder(args);

// Getting started
builder.Configuration
       .AddYamlFile("./settings/appsettings.yml",                                        false, true)
       .AddYamlFile($"./settings/appsettings.{builder.Environment.EnvironmentName}.yml", true,  true)
       .AddEnvironmentVariables();

builder.Host.UseSerilog((_, logger) =>
{
    logger.ReadFrom.Configuration(builder.Configuration).Destructure.JsonNetTypes();
});
builder.Services.AddScrutorDependencyInjectionInProjects();

// 认证
// builder.Services.AddAuthenticationUsingIdentityServer(builder.Configuration.GetSection("WebApiAuthentication:IdentityServer"));
builder.Services.AddAuthenticationUsingKeycloak(builder.Configuration.GetSection("WebApiAuthentication:Keycloak"));

// 权限控制
builder.Services.AddPermissionBasedAuthorization<PermissionBasedAuthorizationService, PermissionRequirement>();

// HTTP
builder.Services.AddHttpCors(HttpCorsPolicy.Loose);
builder.Services.AddCustomControllers();
builder.Services.AddHttpContextAccessor();

// Application
builder.Services.AddCommandPipelineUsageMediatr(AssemblySelector.FromApplicationDependencies(LibraryType.Project));
builder.Services.AddApplicationLogic();
builder.Services.AddApplicationQueries();
builder.Services.AddRepositoryWithSqlServer(builder.Configuration.GetSection("Repository"));

// Others
builder.Services.AddCustomDistributedCache(builder.Configuration.GetSection("DistributedCache"));
builder.Services.AddCustomSwagger(builder.Configuration.GetSection("Swagger"));
builder.Services.AddWeChat(builder.Configuration.GetSection("WeChat"));
builder.Services.AddWeChatAlarm(builder.Configuration.GetSection("WeChat:Alarm"));

// RSA Digital Signature System
builder.Services.Configure<SignatureOptions>(builder.Configuration.GetSection("Signature"));

// Certificate initialization background service
builder.Services.AddHostedService<CertificateInitializationService>();

builder.Services.AddExceptionResponse().SetHandler<ExceptionResponseHandler>(); // 用户级别异常

// builder.Services.AddInfluxDb(builder.Configuration.GetSection("InfluxDb"));
// builder.Services.AddTimeSeriesWithInfluxDb(builder.Configuration.GetSection("InfluxDb:TimeSeries"));
// builder.Services.AddStatsWithInfluxDb(builder.Configuration.GetSection("InfluxDb:Stats"));

builder.Services.AddCustomMapster();
builder.Services.AddEnrichSerilogRequestLogging(builder.Configuration.GetSection("Serilog:EnrichHttpLogging")); // 日志

// ESP Transmits
builder.Services.AddMemoryCache();
// Background services

/////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////
// 2. Build the service and configure the pipeline
var app = builder.Build();

// logger
Log.Information("Device Guard Cloud is starting up");

// Configure the HTTP request pipeline.
app.UseCustomSwagger();
app.UseHttpCors(HttpCorsPolicy.Loose); // 处理跨域请求

app.UseDashboardStaticFiles(); // 提供访问用户上传的静态文件
app.UseStaticFiles();          // 提供 wwwroot

//app.UseEnrichSerilogRequestLogging(); // 内存消耗很大，不适合生产环境
app.UseSerilogRequestLogging();                      // app.UseEnrichSerilogRequestLogging(); 正式环境关闭, 会导致日志过大，但是非常不利于调试
app.UseExceptionResponseHandler();                   // 400 异常处理. 处理由用户(调用者)导致的异常, 需要在异常请求后执行, 这里会把用户异常改为对应的返回代码
app.UseAuthentication();                             // 注意, 认证和授权系统需要在工作单元完成初始化后运行
app.UseMiddleware<LoginUserPersistenceMiddleware>(); // For save authenticated user to the database
app.UseRouting();
app.UseAuthorization();
app.MapControllers();

/////////////////////////////////////////////////////////////////////////////////////////////////
/////////////////////////////////////////////////////////////////////////////////////////////////
// 3. Start the service
try
{
    app.Services.MigrationRepository();
    await app.Services.UpdateDashboardRuleAsync(CancellationToken.None);
}
catch (Exception ex)
{
    Log.Fatal(ex, "Cannot initialize the service");
}

Log.Information("Device Guard started");
app.Run();
