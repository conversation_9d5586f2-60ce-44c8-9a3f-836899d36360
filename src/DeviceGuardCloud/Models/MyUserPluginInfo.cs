using Newtonsoft.Json;

namespace DeviceGuardCloud.WebApi.Models;

/// <summary>
/// �ҵ��û������Ϣ
/// </summary>
public class MyUserPluginInfo
{
    /// <summary>
    /// �������
    /// </summary>
    [JsonProperty(Order = 1)]
    public required string PluginCode { get; set; }

    /// <summary>
    /// ��Ч����
    /// </summary>
    [JsonProperty(Order = 2)]
    public DateTime ValidUntil { get; set; }
}
