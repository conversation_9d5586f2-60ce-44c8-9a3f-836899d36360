using Newtonsoft.Json;

namespace DeviceGuardCloud.WebApi.Models;

/// <summary>
/// 服务器运行环境信息，为访问的客户端提供
/// </summary>
public class ServerEnvModel
{
    /// <summary>
    /// 是否运行在云端
    /// </summary>
    public bool RunningInClouds { get; set; }

    /// <summary>
    /// 访问API的URL前缀
    /// </summary>
    [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
    public string? ApiUrl { get; set; }

    /// <summary>
    /// 设备卫士名称
    /// </summary>
    [JsonProperty(NullValueHandling = NullValueHandling.Ignore)]
    public string? EdgeName { get; set; }
}
