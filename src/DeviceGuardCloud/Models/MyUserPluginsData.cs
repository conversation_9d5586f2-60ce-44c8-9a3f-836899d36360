using DeviceGuardCloud.DomainModel.Plugins;
using Newtonsoft.Json;

namespace DeviceGuardCloud.WebApi.Models;

/// <summary>
/// 用户插件数据，用于签名校验
/// </summary>
public class MyUserPluginsData
{
    /// <summary>
    /// 插件主机类型
    /// </summary>
    [JsonProperty(Order = 0)]
    public PluginHost Host { get; set; }

    /// <summary>
    /// 设备序列号
    /// </summary>
    [JsonProperty(Order = 1)]
    public required string SerialNumber { get; set; }

    /// <summary>
    /// 用户ID
    /// </summary>
    [JsonProperty(Order = 2)]
    public Guid UserId { get; set; }

    /// <summary>
    /// 用户手机号
    /// </summary>
    [JsonProperty(Order = 3)]
    public string? UserPhoneNumber { get; set; }

    /// <summary>
    /// 插件信息列表
    /// </summary>
    [JsonProperty(Order = 4)]
    public required MyUserPluginInfo[] Plugins { get; set; }
}
