using System.ComponentModel.DataAnnotations;
using Scru<PERSON>;

namespace DeviceGuardCloud.DomainModel.AdminRoles;

/// <summary>
/// 管理员角色的字段合法性验证
/// </summary>
[ServiceDescriptor]
public class AdminRoleValidator : IAdminRoleValidator
{
    private readonly IAdminRoleRepository _adminRoleRepository;

    public AdminRoleValidator(IAdminRoleRepository adminRoleRepository)
    {
        _adminRoleRepository = adminRoleRepository;
    }

    /// <summary>
    /// 验证管理员角色的字段合法性
    /// </summary>
    /// <param name="role"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public async Task ValidateAsync(AdminRole role, CancellationToken cancel)
    {
        if (role == null) throw new ArgumentNullException(nameof(role));

        if (role.Permissions == null)
            throw new ValidationException("缺少权限");

        if (string.IsNullOrEmpty(role.Name))
            throw new ValidationException("缺少角色名称");

        if (await _adminRoleRepository.ExistsAsync(x => x.Name == role.Name && x.Id != role.Id, cancel))
            throw new ValidationException($"角色名称 '{role.Name}' 被占用");
    }
}
