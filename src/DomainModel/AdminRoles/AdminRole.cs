using Doulex.DomainDriven;

namespace DeviceGuardCloud.DomainModel.AdminRoles;

public class AdminRole : IEntity<Guid>, IAggregateRoot
{
    /// <summary>
    /// Id
    /// </summary>
    public Guid Id { get; private set; } = Guid.NewGuid();

    /// <summary>
    /// 角色名
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 显示顺序号
    /// </summary>
    public string? SortNumber { get; set; }

    /// <summary>
    /// 角色含有的权限
    /// </summary>
    public string[] Permissions { get; set; } = [];

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; } = DateTime.Now;

    /// <summary>
    /// 乐观锁控制字段
    /// </summary>
    public byte[] RowVersion { get; private set; } = [];
}
