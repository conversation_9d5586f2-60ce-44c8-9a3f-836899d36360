using Doulex;
using Scrutor;
using System.ComponentModel.DataAnnotations;
using Semver;

namespace DeviceGuardCloud.DomainModel.Bundles;

[ServiceDescriptor]
public class BundleValidator : IBundleValidator
{
    private readonly IBundleRepository _bundleRepository;

    public BundleValidator(IBundleRepository bundleRepository)
    {
        _bundleRepository = bundleRepository;
    }

    public async Task ValidateAsync(Bundle bundle, CancellationToken cancel)
    {
        if (bundle.Version.IsNullOrEmpty())
        {
            throw new ValidationException($"请填写版本号!");
        }

        if (!SemVersion.TryParse(bundle.Version, out var semver))
        {
            throw new ValidationException($"版本号格式不正确, 请参考 semver 规范!");
        }

        if (await _bundleRepository.ExistsAsync(x => x.Id != bundle.Id && x.PluginId == bundle.PluginId && x.Version == bundle.Version, cancel))
            throw new ValidationException($"该版本的插件已存在!");
    }
}
