using System.Diagnostics.CodeAnalysis;
using DeviceGuardCloud.DomainModel.Builds;
using Doulex.DomainDriven;

namespace DeviceGuardCloud.DomainModel.Releases;

/// <summary>
/// 软件程序发布管理的版本
/// </summary>
public class Release : IEntity<Guid>, IAggregateRoot
{
    /// <summary>
    /// Id
    /// </summary>
    public Guid Id { get; private set; } = Guid.NewGuid();

    /// <summary>
    /// 软件制品Id
    /// </summary>
    public required Guid ArtifactId { get; set; }

    private readonly string _version;

    /// <summary>
    /// 版本号
    /// 按照 semVer 规则编写
    /// </summary>
    public required string Version
    {
        get => _version;
        [MemberNotNull(nameof(_version))]
        init
        {
            _version        = value;
            SortableVersion = SemverUtil.NormalizeSemver(value);
            IsStable        = !Version.Contains('-');
        }
    }

    public string SortableVersion { get; private set; } = "";

    /// <summary>
    /// 版本号没有 例如 1.0.2-alpha.1 含有 - 就不是稳定版
    /// </summary>
    public bool IsStable { get; private set; }

    /// <summary>
    /// 文件尺寸
    /// </summary>
    public uint FileSize { get; set; }

    /// <summary>
    /// 存储在服务器的路径
    /// </summary>
    public required string FilePath { get; init; }

    /// <summary>
    /// 是否启用, 供管理员控制
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; private set; } = DateTime.Now;

    /// <summary>
    /// 创建人
    /// </summary>
    public Guid CreatedBy { get; init; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// 更新人
    /// </summary>
    public Guid? UpdatedBy { get; set; }

    /// <summary>
    /// 记录更新信息
    /// </summary>
    /// <param name="adminId"></param>
    public void MarkUpdated(Guid adminId)
    {
        UpdatedAt = DateTime.Now;
        UpdatedBy = adminId;
    }

    /// <summary>
    /// 乐观锁控制字段
    /// </summary>
    public byte[] RowVersion { get; private set; } = [];
}
