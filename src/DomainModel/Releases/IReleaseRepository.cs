using Doulex.DomainDriven;

namespace DeviceGuardCloud.DomainModel.Releases;

/// <summary>
/// Release 的仓储接口
/// </summary>
public interface IReleaseRepository : IRepository<Release, Guid>
{
    /// <summary>
    /// 获取指定 Artifact 的最新版本
    /// </summary>
    /// <param name="artifactId"></param>
    /// <param name="stableOnly">仅查询稳定版</param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task<string?> GetLatestVersionAsync(Guid artifactId, bool stableOnly, CancellationToken cancel);

    /// <summary>
    /// 根据 Artifact ID 异步删除多个 Release 记录
    /// </summary>
    /// <param name="artifactId">要删除的 Release 所属 Artifact 的唯一标识</param>
    /// <param name="cancel">用于取消操作的 CancellationToken</param>
    /// <returns>一个表示异步操作的 Task</returns>
    Task RemoveRangeByArtifactIdAsync(Guid artifactId, CancellationToken cancel);

    /// <summary>
    /// 获取指定版本的 Release 信息
    /// </summary>
    /// <param name="version">目标版本号，为空时可能返回默认或最新版本</param>
    /// <param name="cancellationToken">取消操作标识，用于中途取消任务执行</param>
    /// <returns>包含指定版本 Release 的信息对象，或 null 如果未找到</returns>
    Task<Release?> GetVersionByAsync(string version, CancellationToken cancellationToken);
}
