using System.ComponentModel.DataAnnotations;
using Doulex;
using <PERSON><PERSON><PERSON>;
using Semver;

namespace DeviceGuardCloud.DomainModel.Releases;

[ServiceDescriptor]
public class ReleaseValidator : IReleaseValidator
{
    private readonly IReleaseRepository _releaseRepository;

    public ReleaseValidator(IReleaseRepository releaseRepository)
    {
        _releaseRepository = releaseRepository;
    }

    public async Task ValidateAsync(Release release, CancellationToken cancel)
    {
        if (release.Version.IsNullOrEmpty())
        {
            throw new ValidationException($"请填写版本号!");
        }

        if (!SemVersion.TryParse(release.Version, out _))
        {
            throw new ValidationException($"版本号格式不正确, 请参考 semver 规范!");
        }

        if (await _releaseRepository.ExistsAsync(x => x.Id != release.Id && x.ArtifactId == release.ArtifactId && x.Version == release.Version, cancel))
        {
            throw new ValidationException($"该版本的插件已存在!");
        }
    }
}
