using System.Security.Cryptography;
using System.Text;
using Doulex;
using Doulex.DomainDriven;

namespace DeviceGuardCloud.DomainModel.Users;

/// <summary>
/// 系统的用户，这个用户是系统的最终用户。
/// </summary>
public class User : IEntity<Guid>, IAggregateRoot
{
    /// <summary>
    /// 对象的Id
    /// </summary>
    public Guid Id { get; private set; } = Guid.NewGuid();

    /// <summary>
    /// 登录用户名
    /// </summary>
    public string? UserName { get; set; }

    #region 密码

    /// <summary>
    /// 用户的密码
    /// </summary>
    public string? Password { get; private set; }

    /// <summary>
    /// Generate the password
    /// </summary>
    /// <param name="password"></param>
    /// <param name="salt"></param>
    /// <returns></returns>
    private string GeneratePassword(string password, string salt)
    {
        var hash = SHA1.HashData(Encoding.UTF8.GetBytes($"{salt}{password}"));
        return $"{salt}{hash.ToHexString()}";
    }

    /// <summary>
    /// 设置密码
    /// </summary>
    /// <param name="password"></param>
    public void SetPassword(string password)
    {
        var salt = Guid.NewGuid().ToString().Substring(0, 8);
        Password = GeneratePassword(password, salt);
    }

    /// <summary>
    /// 检查密码正确性
    /// </summary>
    /// <param name="password"></param>
    /// <returns></returns>
    public bool CheckPassword(string? password)
    {
        if (string.IsNullOrEmpty(Password))
            return string.IsNullOrEmpty(password);

        var generatePassword = GeneratePassword(password ?? "", Password.Substring(0, 8));
        return string.Equals(Password, generatePassword, StringComparison.OrdinalIgnoreCase);
    }

    #endregion

    /// <summary>
    /// 是否是超级用户
    /// </summary>
    public bool IsSuperUser { get; set; }

    /// <summary>
    /// The url of user avatar from oauth provider
    /// CAUTION: When the url is not available, we should retrieve it from the oauth provider
    /// </summary>
    public string? AvatarUrl { get; set; }

    /// <summary>
    /// 别名
    /// </summary>
    public string? ScreenName { get; set; }

    /// <summary>
    /// 最初从认证中心过来的名字
    /// </summary>
    public string? ScreenNameDefault { get; set; }

    /// <summary>
    /// The user's phone number
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// 手机号是否被确认
    /// </summary>
    public bool PhoneNumberConfirmed { get; set; }

    /// <summary>
    /// The user's email address
    /// </summary>
    public string? Email { get; set; }

    ///// <summary>
    ///// The user's weChat's OpenId
    ///// </summary>
    //public string? OpenId { get; set; }

    /// <summary>
    /// Email是否被确认
    /// </summary>
    public bool EmailConfirmed { get; set; }

    #region 用户登录信息

    /// <summary>
    /// 获取用户的外部登录的对象集合
    /// </summary>
    public IEnumerable<UserLogin> ExternalLogins { get; } = new List<UserLogin>();

    /// <summary>
    /// Add a external login to the user
    /// </summary>
    /// <param name="providerName"></param>
    /// <param name="providerValue"></param>
    /// <exception cref="InvalidOperationException"></exception>
    public void AddOrUpdateExternalLogin(string providerName, string providerValue)
    {
        var logins = ExternalLogins as List<UserLogin> ?? throw new InvalidOperationException();

        var login = logins.FirstOrDefault(l => l.ProviderName == providerName);
        if (login == null)
        {
            login = new UserLogin(Id, providerName, providerValue);
            logins.Add(login);
            return;
        }

        login.ProviderValue = providerValue;
    }

    #endregion

    /// <summary>
    /// 用户在微信的OPENID
    /// </summary>
    public string? WechatOpenId { get; set; }

    /// <summary>
    /// 用户是否被启用(管理员通过该功能启用或禁用用户)
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; private set; } = DateTime.Now;

    /// <summary>
    /// 乐观锁控制字段
    /// </summary>
    public byte[] RowVersion { get; private set; } = [];
}
