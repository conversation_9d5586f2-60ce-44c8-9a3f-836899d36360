using Doulex.DomainDriven;

namespace DeviceGuardCloud.DomainModel.Artifacts;

/// <summary>
/// 软件制品
/// 一个软件一个独立的运行包, 包括 PC 端, 嵌入式等. 
/// </summary>
public class Artifact : IEntity<Guid>, IAggregateRoot
{
    /// <summary>
    /// Id
    /// </summary>
    public Guid Id { get; private set; } = Guid.NewGuid();

    /// <summary>
    /// 模块代码, 不可更改, 用于识别模块
    /// 例如 CFCD
    /// </summary>
    public required string Code { get; init; }

    /// <summary>
    /// 软件制品的类型
    /// </summary>
    public required ArtifactType Type { get; init; }

    /// <summary>
    /// 模块显示名
    /// 例如 英飞源模块
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// 下载权限
    /// </summary>
    public required AccessPermission Permission { get; set; }

    /// <summary>
    /// 描述文本
    /// 例如: 华为EX系列是...
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 最新版本
    /// </summary>
    public string? LatestVersion { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; private set; } = DateTime.Now;

    /// <summary>
    /// 创建人
    /// </summary>
    public Guid CreatedBy { get; init; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedAt { get; private set; }

    /// <summary>
    /// 更新人
    /// </summary>
    public Guid? UpdatedBy { get; private set; }

    /// <summary>
    /// 记录更新
    /// </summary>
    /// <param name="adminId"></param>
    public void MarkUpdated(Guid adminId)
    {
        UpdatedAt = DateTime.Now;
        UpdatedBy = adminId;
    }

    /// <summary>
    /// 乐观锁控制字段
    /// </summary>
    public byte[] RowVersion { get; private set; } = [];
}
