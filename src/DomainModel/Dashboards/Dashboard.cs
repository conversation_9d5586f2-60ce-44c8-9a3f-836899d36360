using Doulex.DomainDriven;

namespace DeviceGuardCloud.DomainModel.Dashboards;

/// <summary>
/// HTTP Redirection 定向记录
/// 用于重定向用户的请求到默认系统还是自定义 dashboard
/// </summary>
public class Dashboard : IEntity<Guid>, IAggregateRoot
{
        
    public Guid Id { get; private set; } = Guid.NewGuid();

    /// <summary>
    /// 名称
    /// </summary>
    public required string Name { get; set; }

    /// <summary>
    /// 仪表盘的类型
    /// </summary>
    public required DashboardType Type { get; set; }

    /// <summary>
    /// 描述信息
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 是否使用根路径直接访问
    /// 最多仅允许一个 dashboard 使用根路径直接访问
    /// </summary>
    public DashboardClientType? EnableRootPathAccess { get; set; }

    /// <summary>
    /// 用户访问的路径
    /// </summary>
    public string Path { get; set; } = "";

    /// <summary>
    /// 当 Type 为 Html 时，指定的首页
    /// </summary>
    public string? IndexPage { get; set; }

    /// <summary>
    /// 部署包尺寸 (BYTE)
    /// </summary>
    public int? FileSize { get; set; }

    /// <summary>
    /// 部署时间
    /// </summary>
    public DateTime? FileUploadTime { get; set; }

    /// <summary>
    /// 已启用
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 创建日期
    /// </summary>
    public DateTime CreatedAt { get; private set; } = DateTime.Now;


    /// <summary>
    /// 乐观锁控制字段
    /// </summary>
    public byte[] RowVersion { get; private set; } = [];
}
