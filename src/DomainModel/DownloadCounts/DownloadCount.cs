using Doulex.DomainDriven;

namespace DeviceGuardCloud.DomainModel.DownloadCounts;

/// <summary>
/// 编译的模块
/// 每编译一个新版本, 产生一个编译记录, 编译包含模块的具体的版本
/// </summary>
public class DownloadCount : IEntity<Guid>, IAggregateRoot
{
    /// <summary>
    /// Id
    /// </summary>
    public Guid Id { get; private set; } = Guid.NewGuid();

    /// <summary>
    /// 对象类型, 例如 plugin 等
    /// </summary>
    public required ObjectType ObjectType { get; set; }

    /// <summary>
    /// 对象Id
    /// </summary>
    public required Guid ObjectId { get; set; }

    /// <summary>
    /// 下载次数
    /// </summary>
    public uint Count { get; private set; }

    /// <summary>
    /// 增加下载次数
    /// </summary>
    public void IncreaseCount()
    {
        Count++;
        UpdatedAt = DateTime.Now;
    }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime UpdatedAt { get; private set; } = DateTime.Now;

    /// <summary>
    /// 乐观锁控制字段
    /// </summary>
    public byte[] RowVersion { get; private set; } = [];
}
