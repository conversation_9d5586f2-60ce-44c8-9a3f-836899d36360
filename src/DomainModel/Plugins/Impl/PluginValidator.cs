using System.ComponentModel.DataAnnotations;
using DeviceGuardCloud.DomainModel.PluginCategories;
using Doulex;
using Scru<PERSON>;

namespace DeviceGuardCloud.DomainModel.Plugins.Impl;

[ServiceDescriptor]
public class PluginValidator : IPluginValidator
{
    private readonly IPluginRepository _pluginRepository;
    private readonly IPluginCategoryRepository _pluginCategoryRepository;

    public PluginValidator(IPluginRepository pluginRepository, IPluginCategoryRepository pluginCategoryRepository)
    {
        _pluginRepository = pluginRepository;
        _pluginCategoryRepository = pluginCategoryRepository;
    }

    public async Task ValidateAsync(Plugin plugin, CancellationToken cancel)
    {
        if (plugin.Code.IsNullOrEmpty())
            throw new ValidationException($"插件编码不能为空!");

        if (await _pluginRepository.ExistsAsync(x => x.Id != plugin.Id && x.Code == plugin.Code, cancel))
            throw new ValidationException($"该插件编码已存在!");

        if (plugin.PluginCategoryId.HasValue)
        {
            if (!await _pluginCategoryRepository.ExistsAsync(x => x.Id == plugin.PluginCategoryId, cancel))
                throw new ValidationException($"该插件分类不存在!");
        }
    }
}