using Doulex.DomainDriven;

namespace DeviceGuardCloud.DomainModel.Builds;

/// <summary>
/// Build 的仓储接口
/// </summary>
public interface IBuildRepository : IRepository<Build, Guid>
{
    /// <summary>
    /// 获取模块的最新版本
    /// </summary>
    /// <param name="moduleId">模块ID</param>
    /// <param name="stableOnly">是否仅稳定版本</param>
    /// <param name="cancel">取消令牌</param>
    /// <returns>最新版本号</returns>
    Task<Build?> GetLatestVersionAsync(Guid moduleId, bool stableOnly, CancellationToken cancel);

    /// <summary>
    /// 获取最佳匹配的构建版本
    /// </summary>
    /// <param name="moduleId"></param>
    /// <param name="minimumVersionRequired">需要的最小版本</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>最佳匹配的构建版本实体</returns>
    Task<Build?> GetBestMatchBuildAsync(Guid moduleId, string minimumVersionRequired, CancellationToken cancellationToken);
}
