using DeviceGuardCloud.DomainModel.Builds;
using <PERSON><PERSON><PERSON>;

namespace DeviceGuardCloud.DomainModel.Modules.Impl;

[ServiceDescriptor]
public class ModuleVersionUpdater : IModuleVersionUpdater
{
    private readonly IBuildRepository  _buildRepository;
    private readonly IModuleRepository _moduleRepository;

    public ModuleVersionUpdater(IBuildRepository buildRepository, IModuleRepository moduleRepository)
    {
        _buildRepository  = buildRepository;
        _moduleRepository = moduleRepository;
    }

    public async Task ApplyLatestVersionAsync(Guid moduleId, CancellationToken cancel)
    {
        var latestVersion = await _buildRepository.GetLatestVersionAsync(moduleId, true, cancel);

        var module = await _moduleRepository.GetAsync(moduleId, cancel) ?? throw new InvalidOperationException("Module not found");
        module.LatestVersion = latestVersion?.Version;
        await _moduleRepository.UpdateAsync(module, cancel);
    }
}
