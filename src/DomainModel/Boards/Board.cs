using Doulex.DomainDriven;

namespace DeviceGuardCloud.DomainModel.Boards;

/// <summary>
/// 检测仪主板信息
/// </summary>
public class Board : IEntity<Guid>, IAggregateRoot
{
    /// <summary>
    /// ID
    /// </summary>
    public Guid Id { get; private set; } = Guid.NewGuid();

    /// <summary>
    /// 主板型号
    /// </summary>
    public required string Model { get; set; }

    /// <summary>
    /// 检测仪序列号
    /// </summary>
    public required string SerialNumber { get; set; }

    /// <summary>
    /// 产品秘钥
    /// </summary>
    public string? ProductKey { get; set; }

    /// <summary>
    /// AES密钥
    /// </summary>
    public required string AesKey { get; set; }

    /// <summary>
    /// 经销商名称
    /// </summary>
    public string? AgencyName { get; set; }

    /// <summary>
    /// 完成一次激活请求, 记录操作
    /// </summary>
    /// <param name="productKey"></param>
    /// <param name="userId"></param>
    public void Activate(string productKey, Guid userId)
    {
        if (!Enabled)
        {
            throw new InvalidOperationException("该设备已被禁用");
        }

        if (OwnedBy.HasValue && OwnedBy != userId)
        {
            throw new InvalidOperationException("该设备已绑定其它用户");
        }

        ProductKey     =  productKey;
        ActivatedCount += 1;

        OwnedBy         = userId;
        LastActivatedAt = DateTime.Now;
    }

    /// <summary>
    /// 请求次数
    /// </summary>
    public uint ActivatedCount { get; set; } = 0;

    /// <summary>
    /// 所有者
    /// </summary>
    public Guid? OwnedBy { get; set; }


    /// <summary>
    /// 是否被启用
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// 创建人
    /// </summary>
    public Guid CreatedBy { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; private set; } = DateTime.Now;

    /// <summary>
    /// 最后一次激活时间
    /// </summary>
    public DateTime LastActivatedAt { get; set; } = DateTime.Now;

    /// <summary>
    /// 更新人
    /// </summary>
    public Guid? UpdatedBy { get; set; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime? UpdatedAt { get; set; }

    /// <summary>
    /// 标记更新
    /// </summary>
    /// <param name="userId">更新人ID</param>
    public void MarkUpdated(Guid userId)
    {
        UpdatedBy = userId;
        UpdatedAt = DateTime.Now;
    }
}
