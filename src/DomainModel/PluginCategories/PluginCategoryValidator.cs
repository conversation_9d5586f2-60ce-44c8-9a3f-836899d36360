using System.ComponentModel.DataAnnotations;
using Doulex;
using <PERSON>ru<PERSON>;

namespace DeviceGuardCloud.DomainModel.PluginCategories;

[ServiceDescriptor]
public class PluginCategoryValidator : IPluginCategoryValidator
{
    private readonly IPluginCategoryRepository _pluginCategoryRepository;

    public PluginCategoryValidator(IPluginCategoryRepository pluginCategoryRepository)
    {
        _pluginCategoryRepository = pluginCategoryRepository;
    }

    public async Task ValidateAsync(PluginCategory pluginCategory, CancellationToken cancel)
    {
        if (pluginCategory.ParentId != Guid.Empty)
        {
            if (!await _pluginCategoryRepository.ExistsAsync(x => x.Id == pluginCategory.ParentId, cancel))
            {
                throw new ValidationException($"父级分类不存在!");
            }
        }

        if (!pluginCategory.Name.IsNullOrEmpty())
        {
            if (await _pluginCategoryRepository.ExistsAsync(x => x.Id != pluginCategory.Id && x.ParentId == pluginCategory.ParentId && x.Name == pluginCategory.Name, cancel))
                throw new ValidationException($"该插件分类名在该组中已存在!");
        }
    }
}
