using DeviceGuardCloud.DomainModel.Dashboards;
using MediatR;

namespace DeviceGuardCloud.Application.Dashboards.Commands;

public class CreateDashboardCommand : IRequest<Guid>
{
    /// <summary>
    /// 名称
    /// </summary>
    public required string Name { get; set; }

    /// <summary>
    /// 仪表盘的类型
    /// </summary>
    public DashboardType Type { get; set; }

    /// <summary>
    /// 描述信息
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 是否使用根路径直接访问
    /// 最多仅允许一个 dashboard 使用根路径直接访问
    /// </summary>
    public DashboardClientType? EnableRootPathAccess { get; set; }

    /// <summary>
    /// 用户访问的路径
    /// </summary>
    public required string Path { get; set; }

    /// <summary>
    /// 当 Type 为 Html 时，指定的首页
    /// </summary>
    public string? IndexPage { get; set; }

    /// <summary>
    /// 已启用
    /// </summary>
    public bool Enabled { get; set; }
}
