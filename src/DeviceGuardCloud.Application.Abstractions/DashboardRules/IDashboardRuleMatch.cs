using DeviceGuardCloud.DomainModel.Dashboards;

namespace DeviceGuardCloud.Application.DashboardRules;

/// <summary>
/// 负责部署EdgeDashboard相关路径的缓存
/// </summary>
public interface IDashboardRuleMatch
{
    /// <summary>
    /// 更新缓存
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task UpdateCacheAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 替换路径
    /// 发送 HTTP 302 重定向
    /// 此时不需要考虑关于映射的问题
    /// </summary>
    /// <param name="requestPath">通过请求获取 dashboard 对象</param>
    /// <param name="dashboardClientType"></param>
    /// <returns></returns>
    DashboardMatchResult? MatchPath(string? requestPath, DashboardClientType dashboardClientType);

    /// <summary>
    /// 通过客户端的请求的URL获取边缘名称
    /// </summary>
    /// <param name="clientUrl"></param>
    /// <returns></returns>
    string? GetEdgeName(string clientUrl);
}
