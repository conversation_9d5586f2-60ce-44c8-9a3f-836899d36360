namespace DeviceGuardCloud.Application.Bundles.Deploys;

/// <summary>
/// 发行包管理, 负责保存用户编译完成并上传的发行包，生成安装包工作
/// </summary>
public interface IBundleFileService
{
    /// <summary>
    /// 创建一个(或更新现有)发行包. 更新依据：版本号相同的Bundle将被覆盖
    /// </summary>
    /// <param name="zipFile">ZIP格式压缩的部署包</param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task<BundleFile> SaveBundleAsync(MemoryStream zipFile, CancellationToken cancel);

    /// <summary>
    /// 获取发行包
    /// </summary>
    /// <param name="bundleFile">要读取的模块的信息</param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task<MemoryStream?> GetPluginAsync(BundleFileKey bundleFile, CancellationToken cancel);

    /// <summary>
    /// 删除指定Id的发行包
    /// </summary>
    /// <param name="bundleFilePath">发行包的文件路径</param>
    /// <param name="throwIfNotExists">如果文件不存在, 是否抛出异常</param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task RemoveAsync(string bundleFilePath, bool throwIfNotExists, CancellationToken cancel);

    /// <summary>
    /// 删除指定插件代码的所有发行包
    /// </summary>
    /// <param name="pluginCode">要删除的插件代码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步操作的任务</returns>
    Task RemoveAllAsync(string pluginCode, CancellationToken cancellationToken);
}
