using DeviceGuardCloud.DomainModel.Plugins;

namespace DeviceGuardCloud.Application.Bundles.Deploys;

// {
//     "id": "mos_igbt_test",
//     "version": "1.0.0-beta.1",
//     "host": "DgWin",
//     "module": {
//         "id": "MosIgbtTest",
//         "version": "1.0.0-beta.1"
//     },
//     "moduleParameter": "{}"
// }

/// <summary>
/// 
/// </summary>
/// <param name="Id">插件代码</param>
/// <param name="Host"></param>
public record BundleManifest(
    string               Id,
    string               Version,
    PluginHost           Host,
    BundleManifestModule Module,
    string               ModuleParameter
);

public record BundleManifestModule(
    string Id,
    string Version
);
