using MediatR;
using Newtonsoft.Json;

namespace DeviceGuardCloud.Application.Releases.Commands;

/// <summary>
/// The creation command of Release
/// </summary>
public class UploadReleaseCommand : IRequest<Guid>
{
    /// <summary>
    /// 要部署的Zip文件包
    /// </summary>
    public MemoryStream? ZipContent { get; set; }

    /// <summary>
    /// 发布的软件制品代码
    /// </summary>
    [JsonIgnore]
    public required string ArtifactCode { get; set; }

    /// <summary>
    /// 发布的版本
    /// </summary>
    public required string Version { get; set; }
}
