using DeviceGuardCloud.Application.Releases.Commands;

namespace DeviceGuardCloud.Application.Releases.Deploys;

/// <summary>
/// 发行包管理, 负责保存用户编译完成并上传的发行包，生成安装包工作
/// </summary>
public interface IReleaseFileService
{
    /// <summary>
    /// 创建一个(或更新现有)发行包. 更新依据：版本号相同的Release将被覆盖
    /// </summary>
    /// <param name="request"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task<ReleaseFile> SaveReleaseAsync(UploadReleaseCommand request, CancellationToken cancel);

    /// <summary>
    /// 获取发行包
    /// </summary>
    /// <param name="releaseFilePath"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Stream GetReleaseAsync(string releaseFilePath, CancellationToken cancel);

    /// <summary>
    /// 删除指定Id的发行包
    /// </summary>
    /// <param name="releaseFilePath">发行包的文件路径</param>
    /// <param name="throwIfNotExists">如果文件不存在, 是否抛出异常</param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task RemoveAsync(string releaseFilePath, bool throwIfNotExists, CancellationToken cancel);

    /// <summary>
    /// 删除指定构件代码的所有发行包
    /// </summary>
    /// <param name="artifactCode">要删除的构件代码</param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task RemoveAllAsync(string artifactCode, CancellationToken cancel);
}
