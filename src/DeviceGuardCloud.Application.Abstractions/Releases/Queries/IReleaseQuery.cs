using LinqAsync.Pages;

namespace DeviceGuardCloud.Application.Releases.Queries;

public interface IReleaseQuery
{
    /// <summary>
    /// 获取指定Id的软件制品包
    /// </summary>
    /// <param name="id"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task<ReleaseViewModel?> GetReleaseAsync(Guid id, CancellationToken cancel);

    /// <summary>
    /// 获取指定条件的软件制品包
    /// </summary>
    /// <param name="query"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task<Page<ReleaseViewModel>> GetReleasesAsync(ReleaseQueryPredicate query, CancellationToken cancel);
}
