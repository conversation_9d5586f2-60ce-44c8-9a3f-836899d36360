using DeviceGuardCloud.Application.Utility;
using MediatR;
using Newtonsoft.Json;

namespace DeviceGuardCloud.Application.Users.Commands;

#nullable disable

/// <summary>
/// Update user command
/// </summary>
public class UpdateUserCommand : DictionaryProperty, IRequest
{
    /// <summary>
    /// The Id of user that will be updated
    /// </summary>
    [JsonIgnore]
    public Guid Id { get; set; }


    /// <summary>
    /// 别名
    /// </summary>
    public string ScreenName { get; set; }


    /// <summary>
    /// 用户是否被启用(管理员通过该功能启用或禁用用户)
    /// </summary>
    public bool Enabled { get; set; }
}
