namespace DeviceGuardCloud.Application.Security.Queries.Models;

/// <summary>
/// 登录管理员用户权限
/// 这个模型用来日常登录, 权限判断
/// </summary>
public class LoginUserModel
{
    public string? GetName()
    {
        return ScreenName ?? UserName ?? PhoneNumber ?? Email ?? "Unknown";
    }

    /// <summary>
    /// 用户Id
    /// </summary>
    public Guid UserId { get; set; }

    /// <summary>
    /// The user's name
    /// </summary>
    public string? UserName { get; set; }

    /// <summary>
    /// 用户名称
    /// </summary>
    public string? ScreenName { get; set; }

    /// <summary>
    /// 是否为管理员账户
    /// </summary>
    public bool IsAdminUser { get; set; }

    /// <summary>
    /// 是否是超级用户
    /// </summary>
    public bool IsSuperUser { get; set; }

    /// <summary>
    /// 权限来源
    /// </summary>
    public ApprovedFrom ApprovedFrom { get; set; }

    /// <summary>
    /// 用户的头像
    /// </summary>
    public string? AvatarUrl { get; set; }


    /// <summary>
    /// 用户的邮件地址
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 用户的手机号
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// 用户是否被启用
    /// </summary>
    public bool Enabled { get; set; }
}
