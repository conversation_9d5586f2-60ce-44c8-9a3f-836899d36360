using DeviceGuardCloud.Application.Security.Queries.Models;

namespace DeviceGuardCloud.Application.Security.Queries;

/// <summary>
/// Used for provide login user information
/// </summary>
public interface ILoginUserQuery
{
    /// <summary>
    /// Retrieve the logged in user from cache or database 
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<LoginUserModel?> GetLoginUserAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Retrieve the logged in admin user from cache or database
    /// </summary>
    /// <returns></returns>
    Task<LoginAdminUserModel?> GetLoginAdminUserAsync(CancellationToken cancellationToken);
}
