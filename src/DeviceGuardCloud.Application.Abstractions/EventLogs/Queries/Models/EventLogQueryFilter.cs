namespace DeviceGuardCloud.Application.EventLogs.Queries.Models;

/// <summary>
/// TB 系统警报信息查询条件
/// </summary>
public class EventLogQueryFilter
{
    /// <summary>
    /// 设备卫士名
    /// </summary>
    public string? EdgeName { get; set; }

    /// <summary>
    /// 事件日志名
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 实例对象
    /// </summary>
    public string? Instance { get; set; }

    /// <summary>
    /// 事件日志的名称，例如: Charging
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 创建时间范围的开始时间（闭区间）
    /// </summary>
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 创建时间范围的结束时间（开区间）
    /// </summary>
    public DateTime? EndTime { get; set; }
}
