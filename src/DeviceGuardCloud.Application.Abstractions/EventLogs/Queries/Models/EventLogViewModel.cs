namespace DeviceGuardCloud.Application.EventLogs.Queries.Models;

/// <summary>
/// 警报的视图模型
/// </summary>
public class EventLogViewModel
{
    /// <summary>
    /// 事件日志的Id
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 事件日志的类型，例如: System
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// 事件日志的名称，例如: Charging
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 事件日志的实例，例如: 1
    /// </summary>
    public string? Instance { get; set; }

    /// <summary>
    /// 事件日志的消息
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 详情
    /// </summary>
    public object? Details { get; set; }

    /// <summary>
    /// 事件日志的创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
