using Newtonsoft.Json;

namespace DeviceGuardCloud.Application.Signatures.Models;

/// <summary>
/// 签名证书响应模型
/// </summary>
public class SignCertificateResponseModel
{
    /// <summary>
    /// 签名证书公钥（PEM格式）
    /// </summary>
    [JsonProperty(Order = 1)]
    public required string PublicKey { get; init; }

    /// <summary>
    /// 证书颁发者信息
    /// </summary>
    [JsonProperty(Order = 2)]
    public required string Issuer { get; init; }

    /// <summary>
    /// 证书主题信息
    /// </summary>
    [JsonProperty(Order = 3)]
    public required string Subject { get; init; }

    /// <summary>
    /// 证书有效期开始时间
    /// </summary>
    [JsonProperty(Order = 4)]
    public required DateTime ValidFrom { get; init; }

    /// <summary>
    /// 证书有效期结束时间
    /// </summary>
    [JsonProperty(Order = 5)]
    public required DateTime ValidTo { get; init; }

    /// <summary>
    /// 证书指纹（SHA256）
    /// </summary>
    [JsonProperty(Order = 6)]
    public required string Fingerprint { get; init; }

    /// <summary>
    /// 证书序列号
    /// </summary>
    [JsonProperty(Order = 7)]
    public required string SerialNumber { get; init; }

    /// <summary>
    /// 证书类型
    /// </summary>
    [JsonProperty(Order = 8)]
    public string CertificateType { get; init; } = "SignCertificate";
}
