using Newtonsoft.Json;

namespace DeviceGuardCloud.Application.Signatures.Models;

/// <summary>
/// 根证书响应模型
/// </summary>
public class RootCertificateResponseModel
{
    /// <summary>
    /// 根证书公钥（PEM格式）
    /// </summary>
    [JsonProperty(Order = 1)]
    public required string PublicKey { get; init; }

    /// <summary>
    /// 证书颁发者信息
    /// </summary>
    [JsonProperty(Order = 2)]
    public required string Issuer { get; init; }

    /// <summary>
    /// 证书有效期开始时间
    /// </summary>
    [JsonProperty(Order = 3)]
    public required DateTime ValidFrom { get; init; }

    /// <summary>
    /// 证书有效期结束时间
    /// </summary>
    [JsonProperty(Order = 4)]
    public required DateTime ValidTo { get; init; }

    /// <summary>
    /// 证书指纹（SHA256）
    /// </summary>
    [JsonProperty(Order = 5)]
    public required string Fingerprint { get; init; }
}
