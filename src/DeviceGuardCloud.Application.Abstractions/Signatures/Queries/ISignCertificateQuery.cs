using DeviceGuardCloud.Application.Signatures.Models;

namespace DeviceGuardCloud.Application.Signatures.Queries;

/// <summary>
/// 签名证书查询接口
/// </summary>
public interface ISignCertificateQuery
{
    /// <summary>
    /// 获取当前签名证书信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>签名证书响应模型</returns>
    Task<SignCertificateResponseModel> GetSignCertificateAsync(CancellationToken cancellationToken);
}
