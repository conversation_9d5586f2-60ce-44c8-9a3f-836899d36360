using DeviceGuardCloud.DomainModel.Modules;
using DeviceGuardCloud.DomainModel.Plugins;

namespace DeviceGuardCloud.Application.Builds.Queries;

public class BuildViewModel
{
    public Guid Id { get; set; }

    /// <summary>
    /// 模块的Id
    /// </summary>
    public Guid ModuleId { get; set; }

    /// <summary>
    /// 模块名称
    /// </summary>
    public string ModuleName { get; set; } = "";

    /// <summary>
    /// 最新版本
    /// </summary>
    public string Version { get; set; } = "";

    public bool IsStable { get; set; }


    /// <summary>
    /// 依赖的运行时版本
    /// </summary>
    public string RuntimeVersion { get; set; } = "";

    /// <summary>
    /// 已启用
    /// </summary>
    public bool Enabled { get; set; }

    public PluginHost Host { get; set; }


    /// <summary>
    /// 创建日期
    /// </summary>
    public DateTime CreatedAt { get; set; }

    public Guid    CreatedBy     { get; set; }
    public string? CreatedByName { get; set; }

    public DateTime? UpdatedAt { get; set; }

    public Guid? UpdatedBy { get; set; }

    public string? UpdatedByName { get; set; }
}
