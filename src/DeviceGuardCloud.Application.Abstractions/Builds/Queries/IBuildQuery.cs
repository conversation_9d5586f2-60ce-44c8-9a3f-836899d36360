using LinqAsync.Pages;

namespace DeviceGuardCloud.Application.Builds.Queries;

public interface IBuildQuery
{
    /// <summary>
    /// 获取指定Id的模块
    /// </summary>
    /// <param name="id"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task<BuildViewModel?> GetBuildAsync(Guid id, CancellationToken cancel);

    /// <summary>
    /// 获取指定条件的模块
    /// </summary>
    /// <param name="query"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task<Page<BuildViewModel>> GetBuildsAsync(BuildQueryPredicate query, CancellationToken cancel);
}
