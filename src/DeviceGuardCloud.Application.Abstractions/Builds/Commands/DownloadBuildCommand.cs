using MediatR;

namespace DeviceGuardCloud.Application.Builds.Commands;

/// <summary>
/// 下载构建包
/// 这个命令在服务器记录下载计数等信息, 并返回下载文件的路径(注意, 不是 URL)
/// </summary>
public class DownloadBuildCommand : IRequest<string>
{
    /// <summary>
    /// 要下载的构建的代码
    /// </summary>
    public required string ModuleCode { get; init; }

    /// <summary>
    /// 指定下载的版本, 如果没有指定, 则下载最新的稳定版
    /// </summary>
    public string? Version { get; set; }
}
