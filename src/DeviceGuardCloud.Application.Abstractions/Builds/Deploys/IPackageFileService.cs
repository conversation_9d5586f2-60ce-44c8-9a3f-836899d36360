namespace DeviceGuardCloud.Application.Builds.Deploys;

/// <summary>
/// 发行包管理, 负责保存用户编译完成并上传的发行包，生成安装包工作
/// </summary>
public interface IPackageFileService
{
    /// <summary>
    /// 创建一个(或更新现有)发行包. 更新依据：版本号相同的Package将被覆盖
    /// </summary>
    /// <param name="zipPackage">ZIP格式压缩的部署包</param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task<Package> SavePackageAsync(MemoryStream zipPackage, CancellationToken cancel);

    /// <summary>
    /// 获取发行包
    /// </summary>
    /// <param name="moduleBuildFilePath"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task<MemoryStream> GetPackageAsync(string moduleBuildFilePath, CancellationToken cancel);

    /// <summary>
    /// 删除指定Id的发行包
    /// </summary>
    /// <param name="packageFilePath">发行包的文件路径</param>
    /// <param name="throwIfNotExists">如果文件不存在, 是否抛出异常</param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task RemoveAsync(string packageFilePath, bool throwIfNotExists, CancellationToken cancel);
}
