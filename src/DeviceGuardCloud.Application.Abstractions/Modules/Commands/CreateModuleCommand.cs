using DeviceGuardCloud.DomainModel.Modules;
using DeviceGuardCloud.DomainModel.Plugins;
using MediatR;

namespace DeviceGuardCloud.Application.Modules.Commands;

/// <summary>
/// 创建模块命令
/// </summary>
public class CreateModuleCommand : IRequest<Guid>
{
    /// <summary>
    /// 模块代码, 不可更改, 用于识别模块
    /// </summary>
    public required string Code { get; init; }

    /// <summary>
    /// 模块显示名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 描述文本
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 运行的平台
    /// </summary>
    public PluginHost Host { get; set; }

    /// <summary>
    /// 已启用
    /// </summary>
    public bool Enabled { get; set; }
}
