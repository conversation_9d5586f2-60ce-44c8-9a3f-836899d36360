<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
        <RootNamespace>DeviceGuardCloud.Application</RootNamespace>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="AutoProperties.Fody" Version="1.25.0">
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="LinqAsync" Version="1.1.0" />
        <PackageReference Include="MediatR.Contracts" Version="2.0.1" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\DomainModel\DomainModel.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Compile Remove="DataGateway\LatestValue\LatestValueTagViewModel.cs" />
      <Compile Remove="DataGateway\LatestValue\DeviceLatestValueQuery.cs" />
      <Compile Remove="DataGateway\LatestValue\TagLatestValueQuery.cs" />
      <Compile Remove="DataGateway\LatestValue\IDeviceTagQuery.cs" />
    </ItemGroup>
</Project>
