using MediatR;

namespace DeviceGuardCloud.Application.Boards.Commands;

public class CreateBoardCommand : IRequest
{
    /// <summary>
    /// 板卡型号
    /// </summary>
    public required string Model { get; set; }

    /// <summary>
    /// 序列号
    /// </summary>
    public required string SerialNumber { get; set; }

    /// <summary>
    /// 所属机构名称
    /// </summary>
    public string? AgencyName { get; set; }

    /// <summary>
    /// 管理员为板子添加拥有者
    /// </summary>
    public Guid? OwnedBy { get; set; }
}
