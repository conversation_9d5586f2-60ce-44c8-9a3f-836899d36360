using System.ComponentModel.DataAnnotations;

namespace DeviceGuardCloud.Application.Utility;

public class RequiredIfGivenAttribute : ValidationAttribute
{
    // Use base constructor to pass a RequiredAttribute as the inner attribute
    public RequiredIfGivenAttribute()
    {
    }

    public bool AllowEmptyStrings { get; set; }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (validationContext.ObjectInstance is DictionaryProperty dp)
        {
            if (dp.HasChanged(validationContext.DisplayName))
            {
                var required = new RequiredAttribute()
                {
                    AllowEmptyStrings = AllowEmptyStrings
                };

                if (!required.IsValid(value))
                {
                    return new ValidationResult(this.ErrorMessage, new[] {validationContext.MemberName}!);
                }
            }
        }

        // If the condition is not met or the validation succeeds, return ValidationResult.Success
        return ValidationResult.Success;
    }
}
