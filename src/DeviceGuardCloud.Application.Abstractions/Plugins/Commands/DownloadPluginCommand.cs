using DeviceGuardCloud.Application.Utility;
using MediatR;

namespace DeviceGuardCloud.Application.Plugins.Commands;

/// <summary>
/// 下载插件命令
/// </summary>
public class DownloadPluginCommand : IRequest<DownloadedFile>
{
    /// <summary>
    /// 表示要解析和处理为代码或GUID的钥匙信息。
    /// </summary>
    public required string Code { get; set; }

    /// <summary>
    /// 要下载的版本, 如果没有指定, 则下载最新版本
    /// </summary>
    public string? Version { get; set; }
}
