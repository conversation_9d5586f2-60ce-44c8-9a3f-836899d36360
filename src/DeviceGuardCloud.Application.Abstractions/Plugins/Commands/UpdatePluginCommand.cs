using DeviceGuardCloud.Application.Utility;
using DeviceGuardCloud.DomainModel.Plugins;
using MediatR;
using Newtonsoft.Json;

namespace DeviceGuardCloud.Application.Plugins.Commands;

/// <summary>
/// 更新Plugin命令
/// </summary>
public class UpdatePluginCommand : DictionaryProperty, IRequest
{
    /// <summary>
    /// 要更新的Id
    /// </summary>
    [JsonIgnore]
    public Guid Id { get; set; }

    /// <summary>
    /// 名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 描述信息
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 用户访问的路径
    /// </summary>
    public Guid? PluginCategoryId { get; set; }

    /// <summary>
    /// 文档的URL
    /// </summary>
    public string? DocsUrl { get; set; }

    /// <summary>
    /// 概览页面的URL
    /// </summary>
    public string? OverviewUrl { get; set; }

    /// <summary>
    /// 运行的平台
    /// </summary>
    public PluginHost? Host { get; set; }

    /// <summary>
    /// 已启用
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 是否为免费插件
    /// </summary>
    public bool? IsFree { get; set; }
}
