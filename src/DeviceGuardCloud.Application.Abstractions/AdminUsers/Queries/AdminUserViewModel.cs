namespace DeviceGuardCloud.Application.AdminUsers.Queries;

/// <summary>
/// 返回单个用户信息的视图模型
/// </summary>
public class AdminUserViewModel
{
    /// <summary>
    /// 对象的Id
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 用户的真实姓名
    /// </summary>
    public string? RealName { get; set; }

    /// <summary>
    /// The url of user avatar from oauth provider
    /// CAUTION: When the url is not available, we should retrieve it from the oauth provider
    /// </summary>
    public string? AvatarUrl { get; set; }

    ///// <summary>
    ///// 用户的职务
    ///// </summary>
    //public string? Duty { get; set; }

    ///// <summary>
    ///// 用户的称谓
    ///// </summary>
    //public string? Title { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// 用户隶属于的部门
    /// </summary>
    public string? Department { get; set; }

    /// <summary>
    /// 用户的描述信息
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 绑定的UserId
    /// </summary>
    public Guid? UserId { get; set; }

    /// <summary>
    /// 用户的姓名, 不是登录名:)
    /// </summary>
    public string? UserScreenName { get; set; }

    public List<AdminUserRoleViewModel>? Roles { get; set; }

    /// <summary>
    /// 用户被启用(如果用户被禁用, 则用户无法以管理员的身份登录系统)
    /// </summary>
    public bool Enabled { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
