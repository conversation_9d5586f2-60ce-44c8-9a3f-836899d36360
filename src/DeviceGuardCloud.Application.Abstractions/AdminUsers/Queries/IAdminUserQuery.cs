using LinqAsync.Pages;

namespace DeviceGuardCloud.Application.AdminUsers.Queries;

/// <summary>
/// The admin user query 
/// </summary>
public interface IAdminUserQuery
{
    /// <summary>
    /// Get all admin users by filter
    /// </summary>
    /// <param name="query"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task<Page<AdminUserViewModel>> GetAdminUsersAsync(AdminUserQueryPredicate query, CancellationToken cancel);

    /// <summary>
    /// Get admin user by id
    /// </summary>
    /// <param name="id"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task<AdminUserViewModel?> GetAdminUserAsync(Guid id, CancellationToken cancel);
}
