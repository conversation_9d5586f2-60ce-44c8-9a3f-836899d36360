using DeviceGuardCloud.Application.Utility;
using MediatR;

namespace DeviceGuardCloud.Application.Artifacts.Commands;

/// <summary>
/// 下载软件命令
/// </summary>
public class DownloadSoftwareCommand : IRequest<DownloadedFile>
{
    /// <summary>
    /// 构件ID
    /// </summary>
    public required string SoftwareCode { get; set; }

    /// <summary>
    /// 要下载的版本, 如果没有指定, 则下载最新版本
    /// </summary>
    public string? Version { get; set; }
}
