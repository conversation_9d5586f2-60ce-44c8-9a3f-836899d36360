using System.Text.Json.Serialization;
using DeviceGuardCloud.Application.Utility;
using MediatR;

namespace DeviceGuardCloud.Application.AdminRoles.Commands;

#nullable disable

/// <summary>
/// The update command of Role
/// </summary>
public class UpdateAdminRoleCommand : DictionaryProperty, IRequest
{
    /// <summary>
    /// Id
    /// </summary>
    [JsonIgnore]
    public Guid Id { get; set; }

    /// <summary>
    /// 角色名
    /// </summary>
    [RequiredIfGiven]
    public string Name { get; set; }

    /// <summary>
    /// 显示顺序号
    /// </summary>
    [RequiredIfGiven]
    public string SortNumber { get; set; }

    /// <summary>
    /// 角色含有的权限
    /// </summary>
    [RequiredIfGiven]
    public string[] Permissions { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string Description { get; set; }
}
