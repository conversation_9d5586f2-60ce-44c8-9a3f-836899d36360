namespace DeviceGuardCloud.Application.AdminRoles.Queries;

/// <summary>
///  查询返回的对象数据
/// </summary>
public class AdminRoleViewModel
{
    /// <summary>
    /// Id
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// 角色名
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 显示顺序号
    /// </summary>
    public string? SortNumber { get; set; }

    /// <summary>
    /// 角色含有的权限
    /// </summary>
    public Guid[] Permissions { get; set; } = [];

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; }
}
