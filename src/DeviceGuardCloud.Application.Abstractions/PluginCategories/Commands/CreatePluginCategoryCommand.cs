using MediatR;

namespace DeviceGuardCloud.Application.PluginCategories.Commands;

/// <summary>
/// 创建新的管理员用户命令
/// </summary>
public class CreatePluginCategoryCommand : IRequest<Guid>
{
    /// <summary>
    /// 分类名称
    /// </summary>
    public required string Name { get; set; }

    /// <summary>
    /// 父级分类ID（可为空）
    /// </summary>
    public Guid? ParentId { get; set; }

    /// <summary>
    /// 分类描述
    /// </summary>
    public string? Description { get; set; }
}
