using DeviceGuardCloud.Application.Utility;
using MediatR;
using Newtonsoft.Json;

namespace DeviceGuardCloud.Application.PluginCategories.Commands;

/// <summary>
/// 更新管理员用户命令
/// </summary>
public class UpdatePluginCategoryCommand : DictionaryProperty, IRequest
{
    /// <summary>
    /// 分类ID
    /// </summary>
    [JsonIgnore]
    public Guid Id { get; set; }

    /// <summary>
    /// 分类名称
    /// </summary>
    public string? Name { get; set; }

    /// <summary>
    /// 父级分类ID（可为空）
    /// </summary>
    public Guid? ParentId { get; set; }

    /// <summary>
    /// 分类描述
    /// </summary>
    public string? Description { get; set; }
}
