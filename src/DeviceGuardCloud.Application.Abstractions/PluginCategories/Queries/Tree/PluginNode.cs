using Newtonsoft.Json;

namespace DeviceGuardCloud.Application.PluginCategories.Queries.Tree;

/// <summary>
/// 插件节点类型，包含版本信息
/// </summary>
public class PluginNode : TreeNode
{
    public static PluginNode Empty { get; } = new PluginNode
    {
        Id = "Null",
    };

    /// <summary>
    /// 插件版本列表
    /// </summary>
    [JsonProperty(Order = 100)]
    public PluginVersion[] Versions { get; init; } = [];

    public override NodeType Type => NodeType.Plugin;
}
