using LinqAsync.Pages;

namespace DeviceGuardCloud.Application.PluginCategories.Queries;

/// <summary>
/// The pluginCategory query 
/// </summary>
public interface IPluginCategoryQuery
{
    /// <summary>
    /// Get all pluginCategorys by filter
    /// </summary>
    /// <param name="query"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task<Page<PluginCategoryViewModel>> GetPluginCategoriesAsync(PluginCategoryQueryPredicate query, CancellationToken cancel);

    /// <summary>
    /// Get pluginCategory by id
    /// </summary>
    /// <param name="id"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task<PluginCategoryViewModel?> GetPluginCategoryAsync(Guid id, CancellationToken cancel);

    
}
