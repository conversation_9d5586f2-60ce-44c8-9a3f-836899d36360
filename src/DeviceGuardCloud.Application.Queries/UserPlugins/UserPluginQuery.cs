using DeviceGuardCloud.Application.UserPlugins.Queries;
using DeviceGuardCloud.DomainModel.Persistence.SqlServer;
using DeviceGuardCloud.DomainModel.UserPlugins;
using LinqAsync.Pages;
using Microsoft.EntityFrameworkCore;
using Scrutor;

namespace DeviceGuardCloud.Application.Queries.UserPlugins
{
    /// <summary>
    /// 用户与指定插件的关系的查询服务
    /// </summary>
    [ServiceDescriptor]
    public class UserPluginQuery : IUserPluginQuery
    {
        private readonly DeviceGuardCloudDbContext _context;

        public UserPluginQuery(DeviceGuardCloudDbContext context)
        {
            _context = context;
        }

        public async Task<UserPluginViewModel?> GetUserPluginAsync(Guid id, CancellationToken cancel)
        {
            var q                   = _context.Set<UserPlugin>().Where(x => x.Id == id).Select(_context);
            var userPluginViewModel = await q.FirstOrDefaultAsync(cancel);
            return userPluginViewModel;
        }

        public async Task<Page<UserPluginViewModel>> GetUserPluginsAsync(
            UserPluginQueryPredicate query,
            CancellationToken        cancel)
        {
            var q = await _context.Set<UserPlugin>()
                                  .AsNoTracking()
                                  .Where(query.Filter, _context)
                                  .Select(_context)
                                  .Order(query.Sort)
                                  .ToPageAsync(query.Page, cancel);
            return q;
        }
    }
}
