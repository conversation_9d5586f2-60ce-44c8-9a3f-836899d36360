using DeviceGuardCloud.Application.Modules.Queries;
using DeviceGuardCloud.DomainModel.Modules;
using DeviceGuardCloud.DomainModel.Persistence.SqlServer;
using LinqAsync.Pages;
using Microsoft.EntityFrameworkCore;
using <PERSON>ru<PERSON>;

namespace DeviceGuardCloud.Application.Queries.Modules;

[ServiceDescriptor]
public class ModuleQuery : IModuleQuery
{
    private readonly DeviceGuardCloudDbContext _context;

    public ModuleQuery(DeviceGuardCloudDbContext context)
    {
        _context = context;
    }

    public Task<ModuleViewModel?> GetModuleAsync(Guid id, CancellationToken cancel)
    {
        return _context.Set<Module>()
                       .Where(m => m.Id == id)
                       .Select()
                       .FirstOrDefaultAsync(cancel);
    }

    public Task<Page<ModuleViewModel>> GetModulesAsync(ModuleQueryPredicate query, CancellationToken cancel)
    {
        var q = _context.Set<Module>()
                        .Where(query.Filter)
                        .Select()
                        .Order(query.Sort);

        return q.ToPageAsync(query.Page, cancel);
    }
}
