using DeviceGuardCloud.Application.Releases.Queries;
using DeviceGuardCloud.DomainModel.Releases;
using DeviceGuardCloud.DomainModel.Persistence.SqlServer;
using LinqAsync.Pages;
using Microsoft.EntityFrameworkCore;
using Scrutor;

namespace DeviceGuardCloud.Application.Queries.Releases;

[ServiceDescriptor]
public class ReleaseQuery : IReleaseQuery
{
    private readonly DeviceGuardCloudDbContext _context;

    public ReleaseQuery(DeviceGuardCloudDbContext context)
    {
        _context = context;
    }

    public Task<ReleaseViewModel?> GetReleaseAsync(Guid id, CancellationToken cancel)
    {
        return _context.Set<Release>()
                       .Where(m => m.Id == id)
                       .Select(_context)
                       .FirstOrDefaultAsync(cancel);
    }

    public Task<Page<ReleaseViewModel>> GetReleasesAsync(ReleaseQueryPredicate query, CancellationToken cancel)
    {
        var q = _context.Set<Release>()
                        .Where(query.Filter)
                        .Order(query.Sort)
                        .Select(_context);

        return q.ToPageAsync(query.Page, cancel);
    }
}
