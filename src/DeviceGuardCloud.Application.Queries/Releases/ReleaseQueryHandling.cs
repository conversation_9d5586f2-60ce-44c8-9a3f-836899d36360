using DeviceGuardCloud.Application.Releases.Queries;
using DeviceGuardCloud.Application.Utility;
using DeviceGuardCloud.DomainModel.Artifacts;
using DeviceGuardCloud.DomainModel.Persistence.SqlServer;
using DeviceGuardCloud.DomainModel.Releases;
using DeviceGuardCloud.DomainModel.Users;
using Doulex;


namespace DeviceGuardCloud.Application.Queries.Releases;

public static class ReleaseQueryHandling
{
    public static IQueryable<Release> Where(this IQueryable<Release> source, ReleaseQueryFilter filter)
    {
        if (filter == null) throw new ArgumentNullException(nameof(filter));

        var q = from a in source
                where filter.ArtifactId == null || a.ArtifactId == filter.ArtifactId
                where filter.Enabled == null || a.Enabled == filter.Enabled
                select a;

        return q;
    }

    public static IQueryable<ReleaseViewModel> Select(this IQueryable<Release> source, DeviceGuardCloudDbContext context)
    {
        return from b in source
               join artifact in context.Set<Artifact>() on b.ArtifactId equals artifact.Id into ownArtifact
               from m in ownArtifact.DefaultIfEmpty()
               join user in context.Set<User>() on b.CreatedBy equals user.Id into createdBy
               from createdByUser in createdBy.DefaultIfEmpty()
               join user in context.Set<User>() on b.UpdatedBy equals user.Id into updatedBy
               from updatedByUser in updatedBy.DefaultIfEmpty()
               select new ReleaseViewModel
               {
                   Id            = b.Id,
                   ArtifactId    = b.ArtifactId,
                   ArtifactCode  = m.Code,
                   ArtifactName  = m.Name,
                   IsStable      = b.IsStable,
                   Version       = b.Version,
                   CreatedAt     = b.CreatedAt,
                   CreatedBy     = b.CreatedBy,
                   CreatedByName = createdByUser.ScreenName,
                   UpdatedAt     = b.UpdatedAt,
                   UpdatedBy     = b.UpdatedBy,
                   UpdatedByName = updatedByUser.ScreenName,
                   Enabled       = b.Enabled,
               };
    }

    public static IOrderedQueryable<Release> Order(this IQueryable<Release> q, ReleaseQuerySort sort)
    {
        return sort.Name switch
        {
            ReleaseQuerySortName.CreatedAt => q.OrderBy(x => x.CreatedAt,       sort.Direction == SortDirection.Asc),
            ReleaseQuerySortName.Version   => q.OrderBy(x => x.SortableVersion, sort.Direction == SortDirection.Asc),
            _                              => throw new ArgumentOutOfRangeException()
        };
    }
}
