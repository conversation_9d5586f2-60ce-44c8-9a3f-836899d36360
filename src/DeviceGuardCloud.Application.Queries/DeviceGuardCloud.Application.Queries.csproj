<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Doulex.AspNetCore.Abstractions" Version="1.2.1" />
      <PackageReference Include="Doulex.DistributedCache" Version="1.2.1" />
      <PackageReference Include="Scrutor" Version="4.2.2" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\AspNetCore.Abstractions\AspNetCore.Abstractions.csproj" />
      <ProjectReference Include="..\DomainModel.Persistence.SqlServer\DomainModel.Persistence.SqlServer.csproj" />
      <ProjectReference Include="..\DeviceGuardCloud.Application.Abstractions\DeviceGuardCloud.Application.Abstractions.csproj" />
      <ProjectReference Include="..\DeviceGuardCloud.Application.Logic\DeviceGuardCloud.Application.Logic.csproj" />
      <ProjectReference Include="..\WeChatApi.Abstractions\WeChatApi.Abstractions.csproj" />
    </ItemGroup>

</Project>
