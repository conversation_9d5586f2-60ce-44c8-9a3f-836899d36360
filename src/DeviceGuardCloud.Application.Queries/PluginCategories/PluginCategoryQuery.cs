using DeviceGuardCloud.Application.PluginCategories.Queries;
using DeviceGuardCloud.DomainModel.PluginCategories;
using DeviceGuardCloud.DomainModel.Persistence.SqlServer;
using LinqAsync.Pages;
using Microsoft.EntityFrameworkCore;
using Scrutor;

namespace DeviceGuardCloud.Application.Queries.PluginCategories;

[ServiceDescriptor]
public class PluginCategoryQuery : IPluginCategoryQuery
{
    private readonly DeviceGuardCloudDbContext _context;

    public PluginCategoryQuery(DeviceGuardCloudDbContext context)
    {
        _context = context;
    }

    public Task<Page<PluginCategoryViewModel>> GetPluginCategoriesAsync(PluginCategoryQueryPredicate query, CancellationToken cancel)
    {
        var q = _context.Set<PluginCategory>()
                        .AsNoTracking()
                        .Where(query.Filter)
                        .Select(_context)
                        .Order(query.Sort)
                        .ToPageAsync(query.Page, cancel);
        return q;
    }

    public async Task<PluginCategoryViewModel?> GetPluginCategoryAsync(Guid id, CancellationToken cancel)
    {
        var q                       = _context.Set<PluginCategory>().Where(x => x.Id == id).Select(_context);
        var pluginCategoryViewModel = await q.FirstOrDefaultAsync(cancel);
        return pluginCategoryViewModel;
    }

    
}
