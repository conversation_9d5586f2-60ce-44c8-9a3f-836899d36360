using DeviceGuardCloud.DomainModel.Builds;

namespace DeviceGuardCloud.Application.Queries.PluginCategories;

public class SemVersionComparer : IComparer<string>
{
    public int Compare(string? x, string? y)
    {
        // 处理 null 值
        if (x == null && y == null) return 0;
        if (x == null) return -1;
        if (y == null) return 1;

        // 使用 SemverUtil 规范化版本号后进行字符串比较
        string normalizedX = SemverUtil.NormalizeSemver(x);
        string normalizedY = SemverUtil.NormalizeSemver(y);

        return string.Compare(normalizedX, normalizedY, StringComparison.Ordinal);
    }
}
