using DeviceGuardCloud.Application.Boards.Queries;
using DeviceGuardCloud.Application.Utility;
using DeviceGuardCloud.DomainModel.Boards;
using DeviceGuardCloud.DomainModel.Persistence.SqlServer;
using DeviceGuardCloud.DomainModel.Users;
using Doulex;


namespace DeviceGuardCloud.Application.Queries.Boards;

public static class BoardQueryHandling
{
    public static IQueryable<Board> Where(this IQueryable<Board> source, BoardQueryFilter filter)
    {
        if (filter == null) throw new ArgumentNullException(nameof(filter));

        var q = from a in source
                where filter.Model == null || a.Model == filter.Model
                where filter.SerialNumber == null || a.SerialNumber == filter.SerialNumber
                select a;

        return q;
    }

    public static IQueryable<BoardViewModel> Select(this IQueryable<Board> source, DeviceGuardCloudDbContext context)
    {
        return from b in source
               join user in context.Set<User>() on b.CreatedBy equals user.Id into createdBy
               from createdByUser in createdBy.DefaultIfEmpty()
               join user in context.Set<User>() on b.UpdatedBy equals user.Id into updatedBy
               from updatedByUser in updatedBy.DefaultIfEmpty()
               join user in context.Set<User>() on b.OwnedBy equals user.Id into ownedBy
               from ownedByUser in ownedBy.DefaultIfEmpty()
               select new BoardViewModel
               {
                   Id             = b.Id,
                   Model          = b.Model,
                   AgencyName     = b.AgencyName,
                   SerialNumber   = b.SerialNumber,
                   ProductKey     = b.ProductKey,
                   ActivatedCount = b.ActivatedCount,
                   OwnedBy        = b.OwnedBy,
                   OwnedByName    = ownedByUser.ScreenName,
                   CreatedAt      = b.CreatedAt,
                   CreatedBy      = b.CreatedBy,
                   CreatedByName  = createdByUser.ScreenName,
                   UpdatedAt      = b.UpdatedAt,
                   UpdatedBy      = b.UpdatedBy,
                   UpdatedByName  = updatedByUser.ScreenName,
               };
    }

    public static IOrderedQueryable<Board> Order(this IQueryable<Board> q, BoardQuerySort sort)
    {
        return sort.Name switch
        {
            BoardQuerySortName.CreatedAt => q.OrderBy(x => x.CreatedAt, sort.Direction == SortDirection.Asc),
            _                            => throw new ArgumentOutOfRangeException()
        };
    }
}
