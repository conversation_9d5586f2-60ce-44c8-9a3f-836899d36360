using DeviceGuardCloud.Application.Audits.Queries;
using DeviceGuardCloud.Application.Audits.Queries.Models;
using DeviceGuardCloud.DomainModel.Audits;
using DeviceGuardCloud.DomainModel.Persistence.SqlServer;
using LinqAsync.Pages;
using Microsoft.EntityFrameworkCore;
using Scrutor;

namespace DeviceGuardCloud.Application.Queries.Audits;

/// <summary>
/// 审计信息的查询服务
/// </summary>
[ServiceDescriptor]
public class AuditQuery : IAuditQuery
{
    private readonly DeviceGuardCloudDbContext _context;

    public AuditQuery(DeviceGuardCloudDbContext context)
    {
        _context = context;
    }

    /// <summary>
    /// Obtain audit logs based on query criteria
    /// </summary>
    /// <param name="query"></param>
    /// <param name="cancellation"></param>
    /// <returns></returns>
    public Task<Page<AuditViewModel>> GetAuditsAsync(AuditQueryPredicate query, CancellationToken cancellation)
    {
        var q = _context.Set<Audit>()
                        .AsNoTracking()
                        .Where(query.Filter)
                        .Select(query.Selector)
                        .Order(query.Sort)
                        .ToPageAsync(query.Page, cancellation);

        return q;
    }
}
