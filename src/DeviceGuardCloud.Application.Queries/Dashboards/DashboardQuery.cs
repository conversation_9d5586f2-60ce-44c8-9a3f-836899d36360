using DeviceGuardCloud.Application.Dashboards.Queries;
using DeviceGuardCloud.DomainModel.Dashboards;
using DeviceGuardCloud.DomainModel.Persistence.SqlServer;
using LinqAsync.Pages;
using Microsoft.EntityFrameworkCore;
using Scrutor;

namespace DeviceGuardCloud.Application.Queries.Dashboards
{
    /// <summary>
    /// 用户与组织关系的查询服务
    /// </summary>
    [ServiceDescriptor]
    public class DashboardQuery : IDashboardQuery
    {
        private readonly DeviceGuardCloudDbContext _context;
        public DashboardQuery(DeviceGuardCloudDbContext context)
        {
            _context = context;
        }
        //public async Task<DashboardViewModel?> GetDashboardAsync(Guid id, DashboardQueryPredicate query, CancellationToken cancel)
        //{
        //    var q = _context.Set<Dashboard>().Where(x => x.Id == id).Select(query.Selector);
        //    var dashboardViewModel = await q.FirstOrDefaultAsync(cancel);
        //    return dashboardViewModel;
        //}
        public async Task<DashboardViewModel?> GetDashboardAsync(Guid id, CancellationToken cancel)
        {
            var q                  = _context.Set<Dashboard>().Where(x => x.Id == id).Select(_context);
            var dashboardViewModel = await q.FirstOrDefaultAsync(cancel);
            return dashboardViewModel;
        }
        public Task<Page<DashboardViewModel>> GetDashboardsAsync(DashboardQueryPredicate query, CancellationToken cancel)
        {

            var q = _context.Set<Dashboard>()
                            .AsNoTracking()
                            .Where(query.Filter)
                            .Select(_context)
                            .Order(query.Sort)
                            .ToPageAsync(query.Page, cancel);

            return q;
        }

       
    }
}
