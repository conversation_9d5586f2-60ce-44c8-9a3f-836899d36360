using DeviceGuardCloud.Application.Artifacts.Queries;
using DeviceGuardCloud.Application.Utility;
using DeviceGuardCloud.DomainModel.Artifacts;
using DeviceGuardCloud.DomainModel.Persistence.SqlServer;
using DeviceGuardCloud.DomainModel.Users;
using Doulex;


namespace DeviceGuardCloud.Application.Queries.Artifacts;

public static class ArtifactQueryHandling
{
    public static IQueryable<Artifact> Where(this IQueryable<Artifact> source, ArtifactQueryFilter filter)
    {
        if (filter == null) throw new ArgumentNullException(nameof(filter));

        var q = from a in source
                where filter.Code == null || a.Code == filter.Code
                where filter.Type == null || a.Type == filter.Type
                where filter.Name == null || a.Name.Contains(filter.Name)
                where filter.Enabled == null || a.Enabled == filter.Enabled
                select a;
        return q;
    }

    public static IQueryable<ArtifactViewModel> Select(this IQueryable<Artifact> source, DeviceGuardCloudDbContext context)
    {
        return from p in source
               join u in context.Set<User>() on p.CreatedBy equals u.Id into createdByAdmins
               from createdByAdmin in createdByAdmins.DefaultIfEmpty()
               join u in context.Set<User>() on p.UpdatedBy equals u.Id into updatedByAdmins
               from updatedByAdmin in updatedByAdmins.DefaultIfEmpty()
               select new ArtifactViewModel
               {
                   Id            = p.Id,
                   Type          = p.Type,
                   Code          = p.Code,
                   Permission    = p.Permission,
                   Name          = p.Name,
                   Description   = p.Description,
                   Enabled       = p.Enabled,
                   LatestVersion = p.LatestVersion,
                   CreatedAt     = p.CreatedAt,
                   CreatedBy     = p.CreatedBy,
                   CreatedByName = createdByAdmin.ScreenName,
                   UpdatedAt     = p.UpdatedAt,
                   UpdatedBy     = p.UpdatedBy,
                   UpdatedByName = updatedByAdmin.ScreenName,
               };
    }

    public static IOrderedQueryable<ArtifactViewModel> Order(this IQueryable<ArtifactViewModel> q, ArtifactQuerySort sort)
    {
        return sort.Name switch
        {
            ArtifactQuerySortName.CreatedAt => q.OrderBy(x => x.CreatedAt, sort.Direction == SortDirection.Asc),
            ArtifactQuerySortName.Name      => q.OrderBy(x => x.Name,      sort.Direction == SortDirection.Asc),
            _                               => throw new ArgumentOutOfRangeException()
        };
    }
}
