using DeviceGuardCloud.Application.DashboardRules;
using DeviceGuardCloud.Application.DashboardRules.Queries;
using DeviceGuardCloud.DomainModel.Dashboards;
using DeviceGuardCloud.DomainModel.Persistence.SqlServer;
using Microsoft.EntityFrameworkCore;
using Scrutor;

namespace DeviceGuardCloud.Application.Queries.DashboardRules;

/// <inheritdoc />
[ServiceDescriptor]
public class DashboardRuleQuery : IDashboardRuleQuery
{
    private readonly DeviceGuardCloudDbContext _dbContext;
    private readonly IDashboardUrl             _dashboardUrl;

    public DashboardRuleQuery(DeviceGuardCloudDbContext dbContext, IDashboardUrl dashboardUrl)
    {
        _dbContext    = dbContext;
        _dashboardUrl = dashboardUrl;
    }

    /// <inheritdoc />
    public async Task<EdgeDashboardRuleQueryModel[]> GetRulesAsync(CancellationToken cancellationToken)
    {
        var q = from d in _dbContext.Set<Dashboard>()
                select new EdgeDashboardRuleQueryModel
                {
                    DashboardId          = d.Id,
                    SourcePath           = d.Path,
                    EnableRootPathAccess = d.EnableRootPathAccess,
                    IndexPage            = d.IndexPage
                };

        var r = await q.ToArrayAsync(cancellationToken: cancellationToken);
        foreach (var item in r)
        {
            item.MappingPath = _dashboardUrl.GetUrl(item.DashboardId);
            item.SourcePath  = item.SourcePath?.ToLower();
            item.IndexPage   = item.IndexPage?.ToLower();
        }

        return r;
    }
}
