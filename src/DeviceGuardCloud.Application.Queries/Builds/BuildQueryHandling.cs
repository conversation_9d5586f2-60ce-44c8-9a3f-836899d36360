using DeviceGuardCloud.Application.Builds.Queries;
using DeviceGuardCloud.Application.Utility;
using DeviceGuardCloud.DomainModel.Builds;
using DeviceGuardCloud.DomainModel.Modules;
using DeviceGuardCloud.DomainModel.Persistence.SqlServer;
using DeviceGuardCloud.DomainModel.Users;
using Doulex;


namespace DeviceGuardCloud.Application.Queries.Builds;

public static class BuildQueryHandling
{
    public static IQueryable<Build> Where(this IQueryable<Build> source, BuildQueryFilter filter)
    {
        if (filter == null) throw new ArgumentNullException(nameof(filter));

        var q = from a in source
                where filter.ModuleId == null || a.ModuleId == filter.ModuleId
                where filter.Enabled == null || a.Enabled == filter.Enabled
                select a;

        return q;
    }

    public static IQueryable<BuildViewModel> Select(this IQueryable<Build> source, DeviceGuardCloudDbContext context)
    {
        return from b in source
               join module in context.Set<Module>() on b.ModuleId equals module.Id into ownModule
               from m in ownModule.DefaultIfEmpty()
               join user in context.Set<User>() on b.CreatedBy equals user.Id into createdBy
               from createdByUser in createdBy.DefaultIfEmpty()
               join user in context.Set<User>() on b.UpdatedBy equals user.Id into updatedBy
               from updatedByUser in updatedBy.DefaultIfEmpty()
               select new BuildViewModel
               {
                   Id            = b.Id,
                   ModuleId      = b.ModuleId,
                   ModuleName    = m.Name,
                   Host          = m.Host,
                   IsStable      = b.IsStable,
                   Version       = b.Version,
                   CreatedAt     = b.CreatedAt,
                   CreatedBy     = b.CreatedBy,
                   CreatedByName = createdByUser.ScreenName,
                   UpdatedAt     = b.UpdatedAt,
                   UpdatedBy     = b.UpdatedBy,
                   UpdatedByName = updatedByUser.ScreenName,
                   Enabled       = b.Enabled,
               };
    }

    public static IOrderedQueryable<Build> Order(this IQueryable<Build> q, BuildQuerySort sort)
    {
        return sort.Name switch
        {
            BuildQuerySortName.CreatedAt => q.OrderBy(x => x.CreatedAt,       sort.Direction == SortDirection.Asc),
            BuildQuerySortName.Version   => q.OrderBy(x => x.SortableVersion, sort.Direction == SortDirection.Asc),
            _                            => throw new ArgumentOutOfRangeException()
        };
    }
}
