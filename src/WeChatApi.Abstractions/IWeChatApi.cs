namespace WeChatApi.Abstractions
{
    public interface IWeChatApi
    {
        /// <summary>
        /// 获取AccessToken
        /// </summary>
        /// <returns></returns>
        Task<string?> GetAccessTokenAsync();

        /// <summary>
        /// 获取已设置的模版列表
        /// </summary>
        /// <returns></returns>
        Task<TemplateListResponse?> GetTemplateListAsync();
        /// <summary>
        /// 发送模版信息
        /// </summary>
        /// <returns></returns>
        Task<bool> SendTemplateMessageAsync(string templateData);
        /// <summary>
        /// 获取已设置的模版列表
        /// </summary>
        /// <returns></returns>
        List<WeChatTemplate>? GetTemplateList();
    }
}
