using System;
using System.Net;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using DeviceGuard.Interface.Cloud;
using Flurl;
using Flurl.Http;

namespace DeviceGuard.Infrastructure.Cloud;

public class LicenseClient : ILicenseClient
{
    private readonly string        _baseUrl = CloudOptions.Url;
    private readonly ILoginSession _loginSession;

    public LicenseClient(ILoginSession loginSession)
    {
        _loginSession = loginSession;
    }

    /// <summary>
    /// 通过 Model 和 serialNumber 激活设备
    /// </summary>
    /// <param name="model"></param>
    /// <param name="serialNumber"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public async Task<string> ActivateLicenseAsync(string model, string serialNumber, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(model)) throw new ArgumentException("Value cannot be null or empty.",        nameof(model));
        if (string.IsNullOrEmpty(serialNumber)) throw new ArgumentException("Value cannot be null or empty.", nameof(serialNumber));

        // 获取登录账号
        var token = await _loginSession.GetAccessTokenAsync(cancellationToken);

        var client = _baseUrl
                     .AppendPathSegment("api/v1/board-license/activate")
                     .AppendQueryParam("model", model)
                     .AppendQueryParam("sn",    serialNumber)
                     .WithOAuthBearerToken(token);

        var response = await client.PostAsync(cancellationToken: cancellationToken);
        if (response.StatusCode != (int)HttpStatusCode.OK)
        {
            // {
            //     "Message": "无效序列号的内容 (CRC)",
            //     "Type": "BadRequest",
            //     "Details": null,
            //     "RequestId": "0HNDRQ2JPM8JJ:00000004",
            //     "Code": null
            // }
            throw new HttpRequestException($"{response.StatusCode} {response.ResponseMessage}");
        }

        var responseModel = await response.GetJsonAsync<ActivateResponseModel>();
        return responseModel.ProductKey;
    }
}

public class ActivateResponseModel
{
    public string ProductKey { get; set; } = "";
}
