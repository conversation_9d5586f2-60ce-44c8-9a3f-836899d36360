using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;


namespace WeChatApi.Service.DependencyInjection;

/// <summary>
/// 注册配置文件中有关微信公众号接口的信息
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// Register DbContexts
    /// </summary>
    /// <param name="services"></param>
    /// <param name="configuration">The configuration</param>
    public static void AddWeChat(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<WeChatOptions>(configuration);
        services.AddHttpClient<WeChatApi>();
    }


    /// <summary>
    /// Register DbContexts
    /// </summary>
    /// <param name="services"></param>
    /// <param name="configuration">The configuration</param>
    public static void AddWeChatAlarm(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<WeChatAlarmOptions>(configuration);
    }
}
