<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>11</LangVersion>
        <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Doulex" Version="5.16.0" />
        <PackageReference Include="MediatR" Version="12.2.0" />
        <PackageReference Include="Microsoft.Extensions.Caching.Redis" Version="2.2.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.3" />
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="8.0.0" />
        <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
        <PackageReference Include="NetEscapades.Configuration.Yaml" Version="3.1.0" />
        <PackageReference Include="Nito.AsyncEx" Version="5.1.2" />

        <PackageReference Include="Doulex.DistributedCache" Version="1.2.1" />
        <PackageReference Include="Doulex.DotNetCore" Version="1.1.2" />
        <PackageReference Include="Polly" Version="8.3.1" />
        <PackageReference Include="Scrutor" Version="4.2.2" />

        <PackageReference Include="Serilog" Version="3.1.1" />
        <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="2.3.0" />
        <PackageReference Include="Serilog.Formatting.Compact" Version="2.0.0" />
        <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
        <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
        <PackageReference Include="Serilog.Sinks.Seq" Version="7.0.0" />

    </ItemGroup>

</Project>
