using Doulex;
using Doulex.DistributedCache.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace DotNetCore.Infrastructure.Caching;

public static class ConfigureDistributedCache
{
    /// <summary>
    /// 配置分布式缓存的实现模块, 目前支持 redis 和 in-memory,
    /// SqlServer 性能不好, 不支持!
    /// </summary>
    /// <remarks>
    /// 在使用例如 IdentityServer 的时候, 如果需要缓存认证信息, 需要首先配置这个模块
    /// </remarks>
    /// <param name="services"></param>
    /// <param name="configuration"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public static IServiceCollection AddCustomDistributedCache(this IServiceCollection services, IConfiguration configuration)
    {
        if (services == null) throw new ArgumentNullException(nameof(services));
        if (configuration == null) throw new ArgumentNullException(nameof(configuration));

        // Find the configuration section for the distributed cache
        var options = configuration.Get<DistributedCacheOptions>();
        if (options == null)
            throw new Exception("The DistributedCacheOptions is not found in the appsettings.json");

        switch (options.Provider?.ToLower())
        {
            case "redis":
                if (options.Redis is not { } redis)
                    throw new Exception("Distributed cache specified Redis but the configuration is not provided");

                if (redis.Server.IsNullOrEmpty())
                    throw new Exception("Distributed cache specified Redis but the server is not provided");

                services.AddDistributedRedisCache(o =>
                {
                    o.ConfigurationOptions = new()
                    {
                        EndPoints          = {$"{redis.Server}:{redis.Port}"},
                        ConnectTimeout     = redis.ConnectTimeout,
                        ResponseTimeout    = redis.ResponseTimeout,
                        SyncTimeout        = redis.SyncTimeout,
                        AbortOnConnectFail = false,
                    };
                    o.InstanceName = redis.InstanceName ?? throw new Exception("Distributed cache specified Redis but the instance name is not provided");
                });
                break;
            case "in-memory":
                services.AddDistributedMemoryCache(); // 把分布式缓存放到内存中
                break;
            default:
                throw new Exception("Distributed cache not specified");
        }

        // object cache
        services.AddDistributedCacheExtensions();

        return services;
    }
}
