<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:conv1="clr-namespace:Hotwheels.Wpf.Conv;assembly=Hotwheels.Wpf">

    <ResourceDictionary.MergedDictionaries>
        <!-- MahApps -->
        <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Controls.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Fonts.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MahApps.Metro;component/Styles/Themes/Light.Blue.xaml" />

        <!-- Material design -->
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.Indigo.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Secondary/MaterialDesignColor.Amber.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesign2.Defaults.xaml" />

        <!--Material Design Extensions-->
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignExtensions;component/Themes/Generic.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignExtensions;component/Themes/MaterialDesignLightTheme.xaml" />
        <!--see refs https://github.com/spiegelp/MaterialDesignExtensions/blob/master/MaterialDesignExtensionsDemo/App.xaml-->

        <!-- Material Design: MahApps Compatibility -->
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.MahApps;component/Themes/MaterialDesignTheme.MahApps.Fonts.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.MahApps;component/Themes/MaterialDesignTheme.MahApps.Flyout.xaml" />
        <ResourceDictionary Source="MaterialDesignComeWithMahApps.xaml" />

        <!-- Controls-->
        <!-- <ResourceDictionary Source="Styles/TopTabControlStyle.xaml" /> -->
        <!-- <ResourceDictionary Source="Styles/ChromeTabControlStyle.xaml" /> -->
        <!-- <ResourceDictionary Source="Styles/DragablzStyle.xaml" /> -->

        <!-- ~1~Override Styles@1@ -->
         <ResourceDictionary Source="pack://application:,,,/DeviceGuard.Styles;component/Override/MaterialDesignTheme.ListBox.Override.xaml" /> 
         <ResourceDictionary Source="pack://application:,,,/DeviceGuard.Styles;component/Override/MaterialDesignTheme.Card.Override.xaml" /> 
        <!-- <ResourceDictionary Source="pack://application:,,,/DeviceGuard.Styles;component/Override/MaterialDesignTheme.Toolbar.Override.xaml" /> -->
    </ResourceDictionary.MergedDictionaries>

    <!--Dialog-->
    <SolidColorBrush x:Key="UserDialog.TitleBar.Background"
                     Color="{DynamicResource Primary100}" />
    <SolidColorBrush x:Key="UserDialog.TitleBar.Foreground"
                     Color="Black" />

    

    <Style TargetType="{x:Type materialDesign:Ripple}">
        <Setter Property="materialDesign:RippleAssist.IsCentered"
                Value="True" />
        <Setter Property="materialDesign:RippleAssist.ClipToBounds"
                Value="True" />
        <Setter Property="materialDesign:RippleAssist.RippleSizeMultiplier"
                Value="100" />
        <Setter Property="materialDesign:RippleAssist.IsDisabled"
                Value="False" />
    </Style>

    <conv1:EnumToTextConverter x:Key="EnumToTextConverter" />
    <conv1:NullToVisibilityConverter x:Key="DisplayNullElseCollapsedConverter"
                                     EmptyStringIsNull="True"
                                     NullValue="Visible"
                                     NotNullValue="Collapsed" />
    <conv1:NullToVisibilityConverter x:Key="DisplayNotNullElseCollapsedConverter"
                                     EmptyStringIsNull="True"
                                     NullValue="Collapsed"
                                     NotNullValue="Visible" />
    <conv1:NullToVisibilityConverter x:Key="DisplayNullElseHiddenConverter"
                                     EmptyStringIsNull="True"
                                     NullValue="Visible"
                                     NotNullValue="Hidden" />
    <conv1:NullToVisibilityConverter x:Key="DisplayNotNullElseHiddenConverter"
                                     EmptyStringIsNull="True"
                                     NullValue="Hidden"
                                     NotNullValue="Visible" />
    <conv1:BooleanToVisibilityConverter x:Key="DisplayTrueElseCollapsedConverter"
                                        True="Visible"
                                        False="Collapsed" />
    <conv1:BooleanToVisibilityConverter x:Key="DisplayTrueElseHiddenConverter"
                                        True="Visible"
                                        False="Hidden" />
    <conv1:BooleanToVisibilityConverter x:Key="DisplayFalseElseCollapsedConverter"
                                        True="Collapsed"
                                        False="Visible" />
    <conv1:BooleanToVisibilityConverter x:Key="DisplayFalseElseHiddenConverter"
                                        True="Hidden"
                                        False="Visible" />
    <conv:EqualityToVisibilityConverter x:Key="EqualityToVisibilityConverter"
                                        Equal="Visible"
                                        NotEqual="Collapsed" />

    <!-- 在 PriorityBinding 时候使用 -->
    <conv1:BooleanPriorityMatchConverter x:Key="UseParameterWhenTrueConverter"
                                         UseParameterWhen="True" />
    <conv1:BooleanPriorityMatchConverter x:Key="UseParameterWhenFalseConverter"
                                         UseParameterWhen="False" />
    <conv1:BooleanPriorityMatchConverter x:Key="UseParameterWhenNullConverter"
                                         UseParameterWhen="Null" />
    <conv1:BooleanPriorityMatchConverter x:Key="UseParameterWhenNotNullConverter"
                                         UseParameterWhen="NotNull" />

</ResourceDictionary>