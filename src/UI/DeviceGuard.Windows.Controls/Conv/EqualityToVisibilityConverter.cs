using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace DeviceGuard.Windows.Controls.Conv;

/// <summary>
/// 根据值相等性转换为可见性
/// </summary>
public class EqualityToVisibilityConverter : IValueConverter
{
    /// <summary>相等时的可见性</summary>
    public Visibility Equal { get; set; } = Visibility.Visible;

    /// <summary>不相等时的可见性</summary>
    public Visibility NotEqual { get; set; } = Visibility.Collapsed;

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value == null && parameter == null)
            return Equal;
        
        if (value == null || parameter == null)
            return NotEqual;

        // 字符串比较
        if (value is string valueStr && parameter is string paramStr)
        {
            return string.Equals(valueStr, paramStr, StringComparison.OrdinalIgnoreCase) ? Equal : NotEqual;
        }

        // 其他类型比较
        return value.Equals(parameter) ? Equal : NotEqual;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
