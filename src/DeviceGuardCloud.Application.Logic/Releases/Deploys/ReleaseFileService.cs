using DeviceGuardCloud.Application.Releases.Commands;
using Doulex;
using <PERSON><PERSON><PERSON>;

namespace DeviceGuardCloud.Application.Releases.Deploys;

[ServiceDescriptor]
public class ReleaseFileService : IReleaseFileService
{
    /// <summary>
    /// 保存部署包的目录
    /// </summary>
    public static readonly string RepoDir = Path.Combine(AppContext.BaseDirectory, "releases");

    public async Task<ReleaseFile> SaveReleaseAsync(UploadReleaseCommand request, CancellationToken cancel)
    {
        if (request.ZipContent == null)
        {
            throw new ArgumentNullException(nameof(request.ZipContent));
        }

        // 把文件按照 ModuleName-Version-Platform-IsStable 的格式保存到指定目录下, 不要解压缩, 因为后期要整个文件直接下载
        var filePath = Path.Combine(request.ArtifactCode, $"{request.ArtifactCode}-{request.Version}.zip");

        var releaseFile = new ReleaseFile
        {
            FilePath = filePath,
        };


        // 把这个文件转换为 ReleaseFile 对象
        var fileFullPath = Path.Combine(RepoDir, filePath);
        Directory.CreateDirectory(Path.GetDirectoryName(fileFullPath) ?? throw new ArgumentNullException(nameof(fileFullPath)));

        await using var fileStream = File.Create(fileFullPath);
        request.ZipContent.Seek(0, SeekOrigin.Begin);
        await request.ZipContent.CopyToAsync(fileStream, cancel);
        await fileStream.FlushAsync(cancel);

        return releaseFile;
    }

    public Stream GetReleaseAsync(string releaseFilePath, CancellationToken cancel)
    {
        var fileFullPath = Path.Combine(RepoDir, releaseFilePath);
        if (!File.Exists(fileFullPath))
        {
            // 判断文件存在
            throw new FileNotFoundException("文件不存在");
        }

        return File.OpenRead(fileFullPath);
    }

    public Task RemoveAsync(string releaseFilePath, bool throwIfNotExist, CancellationToken cancel)
    {
        if (releaseFilePath.IsNullOrEmpty())
        {
            throw new ArgumentNullException(nameof(releaseFilePath));
        }

        releaseFilePath = Path.Combine(RepoDir, releaseFilePath);
        releaseFilePath = Path.GetFullPath(releaseFilePath);

        var fullDir = Path.GetFullPath(RepoDir);
        if (!releaseFilePath.StartsWith(fullDir))
        {
            throw new ArgumentException("The releaseFile path is not valid.");
        }

        if (File.Exists(releaseFilePath))
        {
            File.Delete(releaseFilePath);
        }
        else if (throwIfNotExist)
        {
            throw new FileNotFoundException("The releaseFile file does not exist.");
        }

        return Task.CompletedTask;
    }

    public Task RemoveAllAsync(string artifactCode, CancellationToken cancel)
    {
        if (artifactCode.IsNullOrEmpty())
        {
            throw new ArgumentNullException(nameof(artifactCode));
        }

        var targetDir = Path.Combine(RepoDir, artifactCode);
        if (Directory.Exists(targetDir))
        {
            Directory.Delete(targetDir, true);
        }

        return Task.CompletedTask;
    }
}
