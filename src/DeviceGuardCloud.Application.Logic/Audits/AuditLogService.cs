using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.Security.Queries;
using DeviceGuardCloud.DomainModel.Audits;
using Doulex.DomainDriven;
using Scrutor;

namespace DeviceGuardCloud.Application.Audits;

/// <summary>
/// 审计日志创建输入
/// </summary>
[ServiceDescriptor]
public class AuditLogService : IAuditLogService
{
    private readonly ILoginUserQuery  _loginUserQuery;
    private readonly IAuditRepository _auditRepository;
    private readonly IUnitOfWork      _unitOfWork;

    /// <summary>Initializes a new instance of the <see cref="T:System.Object" /> class.</summary>
    public AuditLogService(ILoginUserQuery loginUserQueries, IAuditRepository auditRepository, IUnitOfWork unitOfWork)
    {
        _loginUserQuery  = loginUserQueries;
        _auditRepository = auditRepository;
        _unitOfWork      = unitOfWork;
    }

    /// <summary>
    /// 创建审计日志
    /// </summary>
    /// <param name="newAuditModel"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task LogAsync(AuditLog newAuditModel, CancellationToken cancellationToken = default)
    {
        if (newAuditModel == null) throw new ArgumentNullException(nameof(newAuditModel));
        var loginUser = await _loginUserQuery.GetLoginUserAsync(cancellationToken);

        var audit = new Audit
        {
            EntityType     = newAuditModel.EntityType,
            EntityId       = newAuditModel.EntityId.ToString() ?? "",
            EntityName     = newAuditModel.EntityName,
            UserId         = loginUser?.UserId,
            UserName       = loginUser?.UserName,
            UserScreenName = loginUser?.ScreenName,
            Operation      = newAuditModel.Operation,
            Description    = newAuditModel.Description,
            Status         = true,
            Details        = newAuditModel.Details,
        };

        await _auditRepository.AddAsync(audit, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
    }
}
