using AspNetCore.Abstractions.Exceptions;
using Doulex.DomainDriven;
using DeviceGuardCloud.Application.AdminUsers.Commands;
using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.DomainModel.AdminUsers;
using MediatR;
using Microsoft.Extensions.Logging;

namespace DeviceGuardCloud.Application.AdminUsers;

/// <summary>
/// 删除一个管理员用户
/// </summary>
public class RemoveAdminUserCommandHandler : IRequestHandler<RemoveAdminUserCommand>
{
    private readonly IAdminUserRepository                   _adminRepository;
    private readonly IUnitOfWork                            _unitOfWork;
    private readonly ILogger<RemoveAdminUserCommandHandler> _logger;
    private readonly IAuditLogService                       _auditLogService;

    /// <summary>
    /// Constructor
    /// </summary>
    public RemoveAdminUserCommandHandler(
        IAdminUserRepository                   adminRepository,
        ILogger<RemoveAdminUserCommandHandler> logger,
        IAuditLogService                       auditLogService,
        IUnitOfWork                            unitOfWork)
    {
        _adminRepository = adminRepository;
        _logger          = logger;
        _auditLogService = auditLogService;
        _unitOfWork      = unitOfWork;
    }

    /// <summary>
    /// 
    /// </summary>
    public async Task Handle(RemoveAdminUserCommand cmd, CancellationToken cancel)
    {
        var admin = await _adminRepository.GetAsync(cmd.Id, cancel);
        if (admin == null)
        {
            _logger.LogWarning("AdminUserId {AdminUserId} does not exists in DB", cmd.Id);
            throw new NotFoundException($"管理员用户 {cmd.Id} 不存在");
        }

        await using var tran = await _unitOfWork.BeginTransactionAsync(cancel);

        // 删除管理员用户
        await _adminRepository.RemoveAsync(admin, cancel);
        await _unitOfWork.SaveChangesAsync(cancel);

        // 记录日志
        await _auditLogService.LogAsync(new AuditLog
            {
                Operation   = AuditLog.Remove,
                EntityType  = nameof(AdminUser),
                EntityId    = admin.Id,
                EntityName  = admin.RealName,
                Description = $"删除管理员用户 '{admin.RealName}'",
            },
            cancel);

        await tran.CommitAsync(cancel);
    }
}
