using AspNetCore.Abstractions.Exceptions;
using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.Bundles.Commands;
using DeviceGuardCloud.Application.Bundles.Deploys;
using DeviceGuardCloud.Application.PluginCategories.Queries;
using DeviceGuardCloud.DomainModel.Bundles;
using DeviceGuardCloud.DomainModel.Plugins;
using Doulex.DomainDriven;
using MediatR;

namespace DeviceGuardCloud.Application.Bundles;

public class RemoveBundleCommandHandler : IRequestHandler<RemoveBundleCommand>
{
    private readonly IBundleRepository     _bundleRepository;
    private readonly IUnitOfWork           _unitOfWork;
    private readonly IAuditLogService      _auditLogService;
    private readonly IBundleFileService    _bundleFileService;
    private readonly IPluginVersionUpdater _pluginVersionUpdater;
    private readonly IPluginDbQuery        _pluginDbQuery;

    public RemoveBundleCommandHandler(
        IBundleRepository     bundleRepository,
        IAuditLogService      auditLogService,
        IUnitOfWork           unitOfWork,
        IBundleFileService    bundleFileService,
        IPluginVersionUpdater pluginVersionUpdater,
        IPluginDbQuery        pluginCategoryTreeQuery)
    {
        _bundleRepository     = bundleRepository;
        _auditLogService      = auditLogService;
        _unitOfWork           = unitOfWork;
        _bundleFileService    = bundleFileService;
        _pluginVersionUpdater = pluginVersionUpdater;
        _pluginDbQuery        = pluginCategoryTreeQuery;
    }

    public async Task Handle(RemoveBundleCommand request, CancellationToken cancellationToken)
    {
        // 1. 读取数据库记录
        var bundle = await _bundleRepository.GetAsync(request.BundleId, cancellationToken);
        if (bundle == null)
            throw new BadRequestException("Bundle not found", nameof(request.BundleId));

        await using var tran = await _unitOfWork.BeginTransactionAsync(cancellationToken);

        // 2. 删除记录
        await _bundleRepository.RemoveAsync(bundle, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // 3. 更新插件的版本
        await _pluginVersionUpdater.ApplyLatestVersionAsync(bundle.PluginId, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // 4. 删除文件 (注意: 删除文件没有事务)
        await _bundleFileService.RemoveAsync(bundle.FilePath, false, cancellationToken);

        // 4. 记录审计日志
        await _auditLogService.LogAsync(
            new AuditLog
            {
                EntityType  = nameof(Bundle),
                EntityId    = bundle.Id,
                EntityName  = bundle.Version,
                Operation   = AuditLog.Remove,
                Description = "删除插件包",
            },
            cancellationToken);

        // 4. 提交数据库事务
        await tran.CommitAsync(cancellationToken);

        // 复位缓存
        _pluginDbQuery.ResetCache();
    }
}
