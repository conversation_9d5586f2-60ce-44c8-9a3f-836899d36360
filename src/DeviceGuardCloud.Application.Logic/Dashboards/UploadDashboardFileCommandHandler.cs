using AspNetCore.Abstractions.Exceptions;
using Doulex.DomainDriven;
using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.DashboardRules;
using DeviceGuardCloud.Application.Dashboards.Commands;
using DeviceGuardCloud.Application.Dashboards.Deploys;
using DeviceGuardCloud.DomainModel.Dashboards;
using MediatR;
using Scrutor;

namespace DeviceGuardCloud.Application.Dashboards;

/// <summary>
/// The update command for dashboard Dashboard 
/// </summary>
[ServiceDescriptor]
public class UploadDashboardFileCommandHandler : IRequestHandler<UploadDashboardFileCommand>
{
    private readonly IDashboardRepository _dashboardRepository;
    private readonly IAuditLogService     _auditLogService;
    private readonly IUnitOfWork          _unitOfWork;
    //private readonly IDashboardCache      _dashboardCache;
    private readonly IDashboardFile      _dashboardFile;
    private readonly IDashboardRuleMatch _dashboardRuleMatch;

    /// <summary>
    /// Constructor
    /// </summary>
    public UploadDashboardFileCommandHandler(
        IDashboardRepository dashboardRepository,
        IAuditLogService     auditLogService,
        IUnitOfWork          unitOfWork,
        IDashboardRuleMatch  dashboardRuleMatch,
        IDashboardFile       dashboardFile)
    {
        _dashboardRepository = dashboardRepository;
        _auditLogService     = auditLogService;
        _unitOfWork          = unitOfWork;
        _dashboardRuleMatch  = dashboardRuleMatch;
        _dashboardFile       = dashboardFile;
    }

    /// <summary>Handles a request</summary>
    /// <param name="request">The request</param>
    /// <param name="cancellation">Cancellation token</param>
    /// <returns>Response from the request</returns>
    public async Task Handle(UploadDashboardFileCommand request, CancellationToken cancellation)
    {
        var dashboard = await _dashboardRepository.GetAsync(request.Id, cancellation);
        if (dashboard == null)
            throw new NotFoundException($"仪表盘 {request.Id} 不存在");

        dashboard.FileSize       = request.ZipContent.Length;
        dashboard.FileUploadTime = DateTime.Now;

        // 启动事务
        await using var trans = await _unitOfWork.BeginTransactionAsync(cancellation);

        // update and save changes
        await _dashboardRepository.UpdateAsync(dashboard, cancellation);
        await _unitOfWork.SaveChangesAsync(cancellation);

        // audit log
        await _auditLogService.LogAsync(new AuditLog
            {
                Operation   = "UpdateFile",
                EntityType  = nameof(Dashboard),
                EntityId    = dashboard.Id,
                EntityName  = dashboard.Name,
                Description = $"更新仪表盘文件包 '{dashboard.Name}'",
            },
            cancellation);

        // 保存文件
        await _dashboardFile.DeployAsync(dashboard.Id, request.ZipContent, cancellation);

        // 提交事务
        await trans.CommitAsync(cancellation);

        // 更新缓存
        await _dashboardRuleMatch.UpdateCacheAsync(cancellation);
    }
}
