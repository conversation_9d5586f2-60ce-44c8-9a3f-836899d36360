using AspNetCore.Abstractions.Exceptions;
using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.Boards.Commands;
using DeviceGuardCloud.Application.Security.Queries;
using DeviceGuardCloud.DomainModel.Boards;
using Doulex;
using Doulex.DomainDriven;
using MediatR;
using Scrutor;

namespace DeviceGuardCloud.Application.Boards
{
    [ServiceDescriptor]
    public class ActivateBoardCommandHandler : IRequestHandler<ActivateBoardCommand, string>
    {
        private readonly IBoardRepository          _repository;
        private readonly IUnitOfWork               _unitOfWork;
        private readonly IAuditLogService          _auditLogService;
        private readonly IBoardValidator           _boardValidator;
        private readonly IActivatorFactoryProducer _activatorFactory;
        private readonly ILoginUserQuery           _loginUserQuery;

        public ActivateBoardCommandHandler(
            IBoardRepository          repository,
            IUnitOfWork               unitOfWork,
            IAuditLogService          auditLogService,
            IBoardValidator           boardValidator,
            IActivatorFactoryProducer activatorFactory,
            ILoginUserQuery           loginUserQuery)
        {
            _repository       = repository;
            _unitOfWork       = unitOfWork;
            _auditLogService  = auditLogService;
            _boardValidator   = boardValidator;
            _activatorFactory = activatorFactory;
            _loginUserQuery   = loginUserQuery;
        }

        public async Task<string> Handle(ActivateBoardCommand request, CancellationToken cancel)
        {
            if (request.Model.IsNullOrEmpty())
            {
                throw new BadRequestException("sku不能为空");
            }

            if (request.SerialNumber.IsNullOrEmpty())
            {
                throw new BadRequestException("serialNumber不能为空");
            }

            var loginUser = await _loginUserQuery.GetLoginUserAsync(cancel) ?? throw new UnauthorizedAccessException("用户未登录");

            await using var tran = await _unitOfWork.BeginTransactionAsync(cancel);

            // 查询是否曾经激活过
            var board = await _repository.GetAsync(x => x.SerialNumber == request.SerialNumber && x.Model == request.Model, cancel);
            if (board == null)
            {
                // 板子必须出厂登记后, 才能执行激活
                throw new BadRequestException("NotFound", "未找到该设备");
            }

            // 激活   
            var activator  = _activatorFactory.CreateActivator(request.Model) ?? throw new NotFoundException("NotFound", $"未找到 '{request.Model}' 类型的激活器");
            var productKey = await activator.ActivateAsync(request.SerialNumber, cancel);

            board.Activate(productKey, loginUser.UserId);
            await _boardValidator.ValidateAsync(board, cancel);
            await _repository.UpdateAsync(board, cancel);
            await _unitOfWork.SaveChangesAsync(cancel);

            // 记录审计日志
            await _auditLogService.LogAsync(new AuditLog
                {
                    Operation   = "Activate",
                    EntityType  = nameof(Board),
                    EntityId    = board.Id,
                    EntityName  = $"{board.Model}-{board.SerialNumber}",
                    Description = $"重激活 {board.Model}-{board.SerialNumber}",
                },
                cancel);
            await tran.CommitAsync(cancel);

            // 返回产品密钥
            return board.ProductKey ?? throw new BadRequestException("NotFound", "未找到产品密钥");
        }
    }
}
