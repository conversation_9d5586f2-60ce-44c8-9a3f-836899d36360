namespace DeviceGuardCloud.Application.Boards.Activators.ChargerDoctor;

/// <summary>
/// 充电桩计算授权码,简单处理即可防止直接拷贝
///
/// ROMSetting.lockingkey [4] 存放4字节加密代码
/// ROMSetting.lockingkey[0] = (UID[0] + UID[1] + UID[2]) xor 0x5a5a5a5aUL;
/// ROMSetting.lockingkey[1] = (UID[1] + 0x345767) xor 0x70001000;
/// ROMSetting.lockingkey[2] = UID[2] + 0x03467001UL;
/// ROMSetting.lockingkey[3] = CRC(lockingkey[3], 3) ^ 0x23a8ce77;
/// 
/// </summary>
public class MakeKey
{
    private const uint KeyMask = 0x23a8ce77;

    /// <summary>
    /// 通过STM芯片　96bit UID  计算16字节(128 bit) 的注册码
    /// </summary>
    /// <param name="id0"></param>
    /// <param name="id1"></param>
    /// <param name="id2"></param>
    /// <returns>4 UINT32 注册码</returns>
    static uint[] Key(uint id0, uint id1, uint id2)
    {
        uint[] k = new uint[4];
        k[0] = (id0 + id1 + id2) ^ 0x5a5a5a5a;
        k[1] = (id1 + 0x345767) ^ 0x70001000;
        k[2] = (id2 + 0x03467001);
        k[3] = Stm32Crc.Stm32IntCalculate(k, 3) ^ KeyMask;
        return k;
    }

    public static string MKey(uint id0, uint id1, uint id2)
    {
        uint[] k = Key(id0, id1, id2);
        uint[] y = new uint[6];
        for (int i = 0; i < k.Length; i++)
            y[i + 1] = k[i];
        y[0] = 15;
        y[5] = Stm32Crc.Stm32IntCalculate(y, 5);
        string r = $"{y[1]:X8}-{y[2]:X8}-{y[3]:X8}-{y[4]:X8}";
        return r;
    }
}
