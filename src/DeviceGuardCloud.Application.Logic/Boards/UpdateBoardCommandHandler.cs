using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.Boards.Commands;
using DeviceGuardCloud.Application.Security.Queries;
using DeviceGuardCloud.DomainModel.Boards;
using Doulex.AspNetCore.Authorization;
using Doulex.DomainDriven;
using MediatR;

namespace DeviceGuardCloud.Application.Boards;

public class UpdateBoardCommandHandler : IRequestHandler<UpdateBoardCommand>
{
    private readonly IBoardRepository _boardRepository;
    private readonly IBoardValidator  _boardValidator;
    private readonly IUnitOfWork      _unitOfWork;
    private readonly IAuditLogService _auditLogService;
    private readonly ILoginUserQuery  _loginUserQuery;

    public UpdateBoardCommandHandler(
        IBoardRepository boardRepository,
        IBoardValidator  boardValidator,
        IUnitOfWork      unitOfWork,
        IAuditLogService auditLogService,
        ILoginUserQuery  loginUserQuery)
    {
        _boardRepository = boardRepository;
        _boardValidator  = boardValidator;
        _unitOfWork      = unitOfWork;
        _auditLogService = auditLogService;
        _loginUserQuery  = loginUserQuery;
    }

    public async Task Handle(UpdateBoardCommand request, CancellationToken cancel)
    {
        var loginUser = await _loginUserQuery.GetLoginUserAsync(cancel) ?? throw new AuthorizeException("用户未登录");

        var board = await _boardRepository.GetAsync(request.Id, cancel);
        if (board == null)
            throw new ArgumentException("检测仪主板不存在");

        request.Apply(board);
        board.MarkUpdated(loginUser.UserId);

        await _boardValidator.ValidateAsync(board, cancel);

        await using var tran = await _unitOfWork.BeginTransactionAsync(cancel);
        await _boardRepository.UpdateAsync(board, cancel);
        await _unitOfWork.SaveChangesAsync(cancel);

        await _auditLogService.LogAsync(new AuditLog
            {
                EntityType  = nameof(Board),
                EntityId    = board.Id,
                EntityName  = board.SerialNumber,
                Operation   = AuditLog.Update,
                Description = $"更新检测仪主板 '{board.SerialNumber}'"
            },
            cancel);

        await tran.CommitAsync(cancel);
    }
}
