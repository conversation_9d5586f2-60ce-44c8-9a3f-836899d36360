using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.Boards.Commands;
using DeviceGuardCloud.DomainModel.Boards;
using Doulex.DomainDriven;
using MediatR;

namespace DeviceGuardCloud.Application.Boards;

public class RemoveBoardCommandHandler : IRequestHandler<RemoveBoardCommand>
{
    private readonly IBoardRepository _boardRepository;
    private readonly IUnitOfWork      _unitOfWork;
    private readonly IAuditLogService _auditLogService;

    public RemoveBoardCommandHandler(
        IBoardRepository boardRepository,
        IUnitOfWork      unitOfWork,
        IAuditLogService auditLogService)
    {
        _boardRepository = boardRepository;
        _unitOfWork      = unitOfWork;
        _auditLogService = auditLogService;
    }

    public async Task Handle(RemoveBoardCommand request, CancellationToken cancel)
    {
        var board = await _boardRepository.GetAsync(request.Id, cancel);
        if (board == null)
            throw new ArgumentException("检测仪主板不存在");

        await using var tran = await _unitOfWork.BeginTransactionAsync(cancel);
        await _boardRepository.RemoveAsync(board, cancel);
        await _unitOfWork.SaveChangesAsync(cancel);

        await _auditLogService.LogAsync(new AuditLog
            {
                EntityType  = nameof(Board),
                EntityId    = board.Id,
                EntityName  = board.SerialNumber,
                Operation   = AuditLog.Remove,
                Description = $"删除检测仪主板 '{board.SerialNumber}'"
            },
            cancel);

        await tran.CommitAsync(cancel);
    }
}
