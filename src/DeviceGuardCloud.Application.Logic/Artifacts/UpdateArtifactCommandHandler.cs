using AspNetCore.Abstractions.Exceptions;
using Doulex.DomainDriven;
using DeviceGuardCloud.Application.Artifacts.Commands;
using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.Security.Queries;
using DeviceGuardCloud.DomainModel.Artifacts;
using Doulex.AspNetCore.Authorization;
using MediatR;

namespace DeviceGuardCloud.Application.Artifacts;

/// <summary>
/// 更新软件制品命令
/// </summary>
public class UpdateArtifactCommandHandler : IRequestHandler<UpdateArtifactCommand>
{
    private readonly IArtifactRepository _artifactRepository;
    private readonly IArtifactValidator  _artifactValidator;
    private readonly IUnitOfWork         _unitOfWork;
    private readonly IAuditLogService    _auditLogService;
    private readonly ILoginUserQuery     _loginUserQuery;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateArtifactCommandHandler(
        IArtifactRepository artifactRepository,
        IUnitOfWork         unitOfWork,
        IArtifactValidator  artifactValidator,
        IAuditLogService    auditLogService,
        ILoginUserQuery     loginUserQuery)
    {
        _artifactRepository = artifactRepository;
        _unitOfWork         = unitOfWork;
        _artifactValidator  = artifactValidator;
        _auditLogService    = auditLogService;
        _loginUserQuery     = loginUserQuery;
    }

    /// <inheritdoc />
    public async Task Handle(UpdateArtifactCommand request, CancellationToken cancel)
    {
        var loginUser = await _loginUserQuery.GetLoginUserAsync(cancel) ?? throw new AuthorizeException("需要管理员登录");

        var artifact = await _artifactRepository.GetAsync(request.Id, cancel);
        if (artifact == null)
            throw new NotFoundException($"软件制品 {request.Id} 不存在");

        // update the entity
        request.Apply(artifact);
        artifact.MarkUpdated(loginUser.UserId);

        // validate the entity
        await _artifactValidator.ValidateAsync(artifact, cancel);

        await using var tran = await _unitOfWork.BeginTransactionAsync(cancel);

        // update and save changes
        await _artifactRepository.UpdateAsync(artifact, cancel);
        await _unitOfWork.SaveChangesAsync(cancel);

        // 记录审计日志
        await _auditLogService.LogAsync(new AuditLog
            {
                Operation   = AuditLog.Update,
                EntityType  = nameof(Artifact),
                EntityId    = artifact.Id,
                EntityName  = artifact.Code,
                Description = $"更新软件制品 '{artifact.Code}'",
            },
            cancel);

        await tran.CommitAsync(cancel);
    }
}
