using AspNetCore.Abstractions.Exceptions;
using Doulex.DomainDriven;
using DeviceGuardCloud.Application.Artifacts.Commands;
using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.Releases.Deploys;
using DeviceGuardCloud.DomainModel.Artifacts;
using DeviceGuardCloud.DomainModel.Releases;
using MediatR;
using Microsoft.Extensions.Logging;

namespace DeviceGuardCloud.Application.Artifacts;

/// <summary>
/// 删除一个软件制品
/// </summary>
public class RemoveArtifactCommandHandler : IRequestHandler<RemoveArtifactCommand>
{
    private readonly IArtifactRepository                   _artifactRepository;
    private readonly IUnitOfWork                           _unitOfWork;
    private readonly ILogger<RemoveArtifactCommandHandler> _logger;
    private readonly IAuditLogService                      _auditLogService;
    private readonly IReleaseRepository                    _releaseRepository;
    private readonly IReleaseFileService                   _releaseFileService;

    /// <summary>
    /// Constructor
    /// </summary>
    public RemoveArtifactCommandHandler(
        IArtifactRepository                   artifactRepository,
        ILogger<RemoveArtifactCommandHandler> logger,
        IAuditLogService                      auditLogService,
        IUnitOfWork                           unitOfWork,
        IReleaseRepository                    releaseRepository,
        IReleaseFileService                   releaseFileService)
    {
        _artifactRepository = artifactRepository;
        _logger             = logger;
        _auditLogService    = auditLogService;
        _unitOfWork         = unitOfWork;
        _releaseRepository  = releaseRepository;
        _releaseFileService = releaseFileService;
    }

    /// <summary>
    /// 
    /// </summary>
    public async Task Handle(RemoveArtifactCommand cmd, CancellationToken cancel)
    {
        var artifact = await _artifactRepository.GetAsync(cmd.Id, cancel);
        if (artifact == null)
        {
            _logger.LogWarning("ArtifactId {ArtifactId} does not exists in DB", cmd.Id);
            throw new NotFoundException($"软件制品 {cmd.Id} 不存在");
        }

        await using var tran = await _unitOfWork.BeginTransactionAsync(cancel);

        // 删除所有 Release
        await _releaseRepository.RemoveRangeByArtifactIdAsync(artifact.Id, cancel);
        await _unitOfWork.SaveChangesAsync(cancel);

        // 删除软件制品
        await _artifactRepository.RemoveAsync(artifact, cancel);
        await _unitOfWork.SaveChangesAsync(cancel);

        // 删除所有 Release 文件
        await _releaseFileService.RemoveAllAsync(artifact.Code, cancel);

        // 记录日志
        await _auditLogService.LogAsync(new AuditLog
            {
                Operation   = AuditLog.Remove,
                EntityType  = nameof(Artifact),
                EntityId    = artifact.Id,
                EntityName  = artifact.Code,
                Description = $"删除软件制品 '{artifact.Code}'",
            },
            cancel);

        await tran.CommitAsync(cancel);
    }
}
