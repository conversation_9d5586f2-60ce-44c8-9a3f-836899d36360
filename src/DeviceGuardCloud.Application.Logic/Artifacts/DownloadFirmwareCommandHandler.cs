using System.Security.Cryptography;
using AspNetCore.Abstractions.Exceptions;
using DeviceGuardCloud.Application.Artifacts.Commands;
using DeviceGuardCloud.Application.Artifacts.Services;
using DeviceGuardCloud.Application.Releases.Deploys;
using DeviceGuardCloud.Application.Utility;
using DeviceGuardCloud.DomainModel.Artifacts;
using DeviceGuardCloud.DomainModel.Boards;
using DeviceGuardCloud.DomainModel.DownloadCounts;
using DeviceGuardCloud.DomainModel.Releases;
using MediatR;

namespace DeviceGuardCloud.Application.Artifacts;

/// <summary>
/// 下载 Software 命令处理器
/// </summary>
public class DownloadFirmwareCommandHandler : IRequestHandler<DownloadFirmwareCommand, DownloadedFile>
{
    private readonly IArtifactRepository          _artifactRepository;
    private readonly IDownloadCountService        _downloadCountService;
    private readonly IReleaseRepository           _releaseRepository;
    private readonly IReleaseFileService          _releaseFileService;
    private readonly IBoardRepository             _boardRepository;
    private readonly IArtifactPermissionValidator _artifactPermissionValidator;


    /// <summary>
    /// 构造函数
    /// </summary>
    public DownloadFirmwareCommandHandler(
        IArtifactRepository          artifactRepository,
        IDownloadCountService        downloadCountService,
        IReleaseRepository           releaseRepository,
        IReleaseFileService          releaseFileService,
        IBoardRepository             boardRepository,
        IArtifactPermissionValidator artifactPermissionValidator)
    {
        _artifactRepository          = artifactRepository;
        _downloadCountService        = downloadCountService;
        _releaseRepository           = releaseRepository;
        _releaseFileService          = releaseFileService;
        _boardRepository             = boardRepository;
        _artifactPermissionValidator = artifactPermissionValidator;
    }

    /// <summary>
    /// 处理下载命令
    /// </summary>
    /// <param name="request">下载命令</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>下载命令结果</returns>
    public async Task<DownloadedFile> Handle(DownloadFirmwareCommand request, CancellationToken cancellationToken)
    {
        var artifact = await _artifactRepository.GetAsync(x => x.Code == request.BoardModel, cancellationToken) ?? throw new NotFoundException("软件制品不存在");

        if (artifact.Type != ArtifactType.Firmware)
        {
            throw new BadRequestException("请指定有效的软件制品, 仅支持固件");
        }

        // 2. 检查用户权限
        await _artifactPermissionValidator.EnsureCheckUserPermissionAsync(artifact.Permission, cancellationToken);

        // 下载最匹配的版本
        var version = request.Version ?? artifact.LatestVersion ?? throw new BadRequestException("请指定有效版本号");
        var release = await _releaseRepository.GetVersionByAsync(version, cancellationToken) ?? throw new BadRequestException("软件制品版本不存在");

        var stream = _releaseFileService.GetReleaseAsync(release.FilePath, cancellationToken);

        var board = await _boardRepository.GetAsync(x => x.Model == request.BoardModel, cancellationToken) ?? throw new BadRequestException("请指定有效的板型");

        // 使用 board.AesKey 和一个随机的 IV, 使用 Aes CBC 加密 stream, 并把 IV 放到 stream 的开头
        using (var aes = Aes.Create())
        {
            aes.Key     = Convert.FromBase64String(board.AesKey);
            aes.Mode    = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;

            var       iv           = aes.IV; // 随机生成IV
            using var encryptor    = aes.CreateEncryptor();
            using var memoryStream = new MemoryStream();
            await memoryStream.WriteAsync(iv, 0, iv.Length, cancellationToken); // 将 IV 写入流的开头

            await using var cryptoStream = new CryptoStream(memoryStream, encryptor, CryptoStreamMode.Write);
            await stream.CopyToAsync(cryptoStream, cancellationToken);

            stream = new MemoryStream(memoryStream.ToArray());
        }

        var result = new DownloadedFile()
        {
            FileName   = Path.GetFileName(release.FilePath),
            FileStream = stream,
        };

        // 4. 更新下载计数
        await _downloadCountService.UpdateDownloadCountAsync(
            ObjectType.Artifact,
            artifact.Id,
            cancellationToken);

        // 5. 返回成功结果和文件路径
        return result;
    }
}
