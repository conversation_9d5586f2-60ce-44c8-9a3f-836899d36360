using AspNetCore.Abstractions.Exceptions;
using Doulex.DomainDriven;
using DeviceGuardCloud.Application.Builds.Commands;
using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.DomainModel.Builds;
using DeviceGuardCloud.DomainModel.Modules;
using MediatR;

namespace DeviceGuardCloud.Application.Builds;

/// <summary>
/// 更新模块包命令
/// </summary>
public class UpdateBuildCommandHandler : IRequestHandler<UpdateBuildCommand>
{
    private readonly IBuildRepository      _buildRepository;
    private readonly IBuildValidator       _buildValidator;
    private readonly IUnitOfWork           _unitOfWork;
    private readonly IAuditLogService      _auditLogService;
    private readonly IModuleVersionUpdater _moduleVersionUpdater;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateBuildCommandHandler(
        IBuildRepository      buildRepository,
        IUnitOfWork           unitOfWork,
        IBuildValidator       buildValidator,
        IAuditLogService      auditLogService,
        IModuleVersionUpdater moduleVersionUpdater)
    {
        _buildRepository      = buildRepository;
        _unitOfWork           = unitOfWork;
        _buildValidator       = buildValidator;
        _auditLogService      = auditLogService;
        _moduleVersionUpdater = moduleVersionUpdater;
    }

    /// <inheritdoc />
    public async Task Handle(UpdateBuildCommand request, CancellationToken cancel)
    {
        var build = await _buildRepository.GetAsync(request.Id, cancel);
        if (build == null)
            throw new NotFoundException($"模块包 {request.Id} 不存在");

        // update the entity
        request.Apply(build);

        // validate the entity
        await _buildValidator.ValidateAsync(build, cancel);

        await using var tran = await _unitOfWork.BeginTransactionAsync(cancel);

        // update and save changes
        await _buildRepository.UpdateAsync(build, cancel);
        await _unitOfWork.SaveChangesAsync(cancel);

        // Update the module's latest version
        await _moduleVersionUpdater.ApplyLatestVersionAsync(build.ModuleId, cancel);
        await _unitOfWork.SaveChangesAsync(cancel);

        // 记录审计日志
        await _auditLogService.LogAsync(new AuditLog
            {
                Operation   = AuditLog.Update,
                EntityType  = nameof(Build),
                EntityId    = build.Id,
                EntityName  = build.Version,
                Description = $"更新模块包 '{build.Version}'",
            },
            cancel);

        await tran.CommitAsync(cancel);
    }
}
