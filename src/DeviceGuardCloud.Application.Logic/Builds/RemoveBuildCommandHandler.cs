using AspNetCore.Abstractions.Exceptions;
using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.Builds.Commands;
using DeviceGuardCloud.Application.Builds.Deploys;
using DeviceGuardCloud.DomainModel.Builds;
using DeviceGuardCloud.DomainModel.Modules;
using Doulex.DomainDriven;
using MediatR;

namespace DeviceGuardCloud.Application.Builds;

public class RemoveBuildCommandHandler : IRequestHandler<RemoveBuildCommand>
{
    private readonly IBuildRepository      _buildRepository;
    private readonly IUnitOfWork           _unitOfWork;
    private readonly IAuditLogService      _auditLogService;
    private readonly IPackageFileService   _packageFileService;
    private readonly IModuleVersionUpdater _moduleVersionUpdater;


    public RemoveBuildCommandHandler(
        IBuildRepository      buildRepository,
        IAuditLogService      auditLogService,
        IUnitOfWork           unitOfWork,
        IPackageFileService   packageFileService,
        IModuleRepository     moduleRepository,
        IModuleVersionUpdater moduleVersionUpdater)
    {
        _buildRepository      = buildRepository;
        _auditLogService      = auditLogService;
        _unitOfWork           = unitOfWork;
        _packageFileService   = packageFileService;
        _moduleVersionUpdater = moduleVersionUpdater;
    }

    public async Task Handle(RemoveBuildCommand request, CancellationToken cancellationToken)
    {
        // 1. 读取数据库记录
        var build = await _buildRepository.GetAsync(request.BuildId, cancellationToken);
        if (build == null)
            throw new BadRequestException("Build not found", nameof(request.BuildId));

        await using var tran = await _unitOfWork.BeginTransactionAsync(cancellationToken);

        // 2. 删除记录
        await _buildRepository.RemoveAsync(build, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // 更新 Module 的最新版本
        await _moduleVersionUpdater.ApplyLatestVersionAsync(build.ModuleId, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // 3. 删除文件 (注意: 删除文件没有事务)
        await _packageFileService.RemoveAsync(build.FilePath, false, cancellationToken);

        // 4. 记录审计日志
        await _auditLogService.LogAsync(
            new AuditLog
            {
                EntityType  = nameof(Build),
                EntityId    = build.Id,
                EntityName  = build.Version,
                Operation   = AuditLog.Remove,
                Description = "删除模块包",
            },
            cancellationToken);

        // 4. 提交数据库事务
        await tran.CommitAsync(cancellationToken);
    }
}
