using AspNetCore.Abstractions.Exceptions;
using Doulex.DomainDriven;
using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.Users.Commands;
using DeviceGuardCloud.DomainModel.Users;
using MediatR;
using Microsoft.Extensions.Logging;
using Sc<PERSON>tor;

namespace DeviceGuardCloud.Application.Users;

/// <summary>
/// The Creation command for entity Users 
/// </summary>
[ServiceDescriptor]
public class UpdateUserCommandHandler : IRequestHandler<UpdateUserCommand>
{
    private readonly IAuditLogService                  _auditLogService;
    private readonly IUserRepository                   _userRepository;
    private readonly IUnitOfWork                       _unitOfWork;
    private readonly ILogger<UpdateUserCommandHandler> _logger;

    /// <summary>
    /// Constructor
    /// </summary>
    public UpdateUserCommandHandler(
        IUserRepository                   userRepository,
        ILogger<UpdateUserCommandHandler> logger,
        IAuditLogService                  auditLogService,
        IUnitOfWork                       unitOfWork)
    {
        _userRepository  = userRepository;
        _logger          = logger;
        _auditLogService = auditLogService;
        _unitOfWork      = unitOfWork;
    }

    /// <summary>Handles a request</summary>
    /// <param name="cmd">The request</param>
    /// <param name="cancel">Cancellation token</param>
    /// <returns>Response from the request</returns>
    public async Task Handle(UpdateUserCommand cmd, CancellationToken cancel)
    {
        // try to find user entity
        var user = await _userRepository.GetAsync(cmd.Id, cancel);
        if (user == null)
        {
            _logger.LogDebug("User not found: '{Id}'", cmd.Id);
            throw new NotFoundException($"要更新的用户不存在: '{cmd.Id}'");
        }

        // check if username already exists
        //if (cmd.HasChanged(nameof(cmd.UserName)))
        //{
        //    if (!cmd.UserName.IsNullOrEmpty() && cmd.UserName != user.UserName)
        //    {
        //        if (await _userRepository.ExistsAsync(x => x.Id != cmd.Id && x.UserName == cmd.UserName, cancel))
        //        {
        //            _logger.LogDebug("User name already exists: '{UserName}'", cmd.UserName);
        //            throw new BadRequestException($"用户的用户名已被占用: '{cmd.UserName}'");
        //        }

        //        user.UserName = cmd.UserName;
        //    }
        //}

        //// check and setup phoneNumber
        //if (cmd.HasChanged(nameof(cmd.PhoneNumber)))
        //{
        //    if (!cmd.PhoneNumber.IsNullOrEmpty() && cmd.PhoneNumber != user.PhoneNumber)
        //    {
        //        if (await _userRepository.ExistsAsync(x => x.Id != cmd.Id && x.PhoneNumber == cmd.PhoneNumber, cancel))
        //        {
        //            _logger.LogDebug("Phone number already exists: '{PhoneNumber}'", cmd.PhoneNumber);
        //            throw new BadRequestException($"用户的手机号码已被占用: '{cmd.PhoneNumber}'");
        //        }

        //        user.PhoneNumber          = cmd.PhoneNumber;
        //        user.PhoneNumberConfirmed = false;
        //    }
        //}

        //// check and setup email
        //if (cmd.HasChanged(nameof(cmd.Email)))
        //{
        //    if (!cmd.Email.IsNullOrEmpty() && cmd.Email != user.Email)
        //    {
        //        if (await _userRepository.ExistsAsync(x => x.Id != cmd.Id && x.Email == cmd.Email, cancel))
        //        {
        //            _logger.LogDebug("Email already exists: '{Email}'", cmd.Email);
        //            throw new BadRequestException($"用户的邮箱已被占用: '{cmd.Email}'");
        //        }

        //        user.Email          = cmd.Email;
        //        user.EmailConfirmed = false;
        //    }
        //}

        //if (cmd.HasChanged(nameof(cmd.AvatarUrl))) _   = user.AvatarUrl   = cmd.AvatarUrl;
        //if (cmd.HasChanged(nameof(cmd.ScreenName))) _  = user.ScreenName  = cmd.ScreenName;
        //if (cmd.HasChanged(nameof(cmd.IsSuperUser))) _ = user.IsSuperUser = cmd.IsSuperUser;
        //if (cmd.HasChanged(nameof(cmd.Description))) _ = user.Description = cmd.Description;
        cmd.Apply(user);
        await using var tran = await _unitOfWork.BeginTransactionAsync(cancel);
        await _userRepository.AddAsync(user, cancel);
        await _unitOfWork.SaveChangesAsync(cancel);

        await _auditLogService.LogAsync(new AuditLog
            {
                Operation   = AuditLog.Update,
                EntityType  = nameof(User),
                EntityId    = user.Id,
                EntityName  = user.ScreenName,
                Description = $"修改用户 '{user.ScreenName}'",
            },
            cancel);

        await tran.CommitAsync(cancel);
    }
}
