using DeviceGuardCloud.Application.Signatures.Models;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Scrutor;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;

namespace DeviceGuardCloud.Application.Signatures;

/// <summary>
/// RSA数字签名服务实现
/// </summary>
[ServiceDescriptor]
public class SignatureService : ISignatureService
{
    private readonly SignatureOptions          _options;
    private readonly ICertificateManager       _certificateManager;
    private readonly IDistributedCache         _distributedCache;
    private readonly ILogger<SignatureService> _logger;

    private const string RootCertCacheKey       = "Signature:RootCertificate";
    private const string SignCertCacheKey       = "Signature:SignCertificate";
    private const string SignPrivateKeyCacheKey = "Signature:SignPrivateKey";

    public SignatureService(
        IOptions<SignatureOptions> options,
        ICertificateManager        certificateManager,
        IDistributedCache          distributedCache,
        ILogger<SignatureService>  logger)
    {
        _options            = options.Value;
        _certificateManager = certificateManager;
        _distributedCache   = distributedCache;
        _logger             = logger;
    }

    /// <summary>
    /// 对JSON数据进行数字签名
    /// </summary>
    public async Task<SignatureResponseModel> SignJsonAsync(object data, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting JSON signing process");

        try
        {
            // 确保证书存在
            await _certificateManager.EnsureRootCertificateExistsAsync();
            await _certificateManager.EnsureSignCertificateExistsAsync();

            // 生成服务器端随机数
            var nonce     = GenerateNonce();
            var timestamp = DateTime.UtcNow;


            // 将响应数据转换为 JObject 以便签名
            var serializerSettings = new JsonSerializerSettings
            {
                Converters = [new StringEnumConverter(new CamelCaseNamingStrategy())],
                ContractResolver = new CamelCasePropertyNamesContractResolver()
            };

            var jsonData = JObject.FromObject(data, JsonSerializer.Create(serializerSettings));

            // 构建要签名的数据（包含原始数据、随机数和时间戳）
            var signatureData = new
            {
                Data      = jsonData,
                Nonce     = nonce,
                Timestamp = timestamp
            };

            var signatureDataJson  = JsonConvert.SerializeObject(signatureData, serializerSettings);
            var signatureDataBytes = Encoding.UTF8.GetBytes(signatureDataJson);

            // 计算数据哈希
            using var sha256   = SHA256.Create();
            var       dataHash = Convert.ToBase64String(sha256.ComputeHash(signatureDataBytes));

            // 加载签名证书私钥进行签名
            using var privateKey      = await LoadSignCertificatePrivateKeyAsync(cancellationToken);
            var       signature       = privateKey.SignData(signatureDataBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
            var       signatureBase64 = Convert.ToBase64String(signature);

            // 获取签名证书公钥
            var signCertPublicKey = await GetSignCertificatePublicKeyInternalAsync(cancellationToken);

            _logger.LogInformation("JSON signing completed successfully");

            return new SignatureResponseModel
            {
                Data                     = jsonData,
                Signature                = signatureBase64,
                SignCertificatePublicKey = signCertPublicKey,
                Algorithm                = "SHA256withRSA",
                Nonce                    = nonce,
                SignatureTimestamp       = timestamp,
                DataHash                 = dataHash
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sign JSON data");
            throw;
        }
    }

    /// <summary>
    /// 强制重新生成签名证书（不涉及根证书）
    /// </summary>
    public async Task RegenerateCertificateAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Starting sign certificate regeneration");

        try
        {
            // 重新生成签名证书（不涉及根证书）
            await _certificateManager.RegenerateSignCertificateAsync();

            // 清除签名证书缓存
            await ClearCertificateCacheAsync(cancellationToken);

            _logger.LogInformation("Sign certificate regeneration completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to regenerate sign certificate");
            throw;
        }
    }

    /// <summary>
    /// 获取根证书公钥
    /// </summary>
    public async Task<string> GetRootCertificatePublicKeyAsync(CancellationToken cancellationToken)
    {
        try
        {
            // 尝试直接从文件系统加载，根证书通常已在启动时确保存在
            await _certificateManager.EnsureRootCertificateExistsAsync();
            var rootCert     = await _certificateManager.LoadRootCertificateAsync();
            var publicKeyPem = Convert.ToBase64String(rootCert.Export(X509ContentType.Cert));
            var pemFormatted = FormatAsPem(publicKeyPem, "CERTIFICATE");

            _logger.LogDebug("Root certificate loaded from file system");
            return pemFormatted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get root certificate public key");
            throw;
        }
    }

    /// <summary>
    /// 获取当前签名证书公钥
    /// </summary>
    public async Task<string> GetSignCertificatePublicKeyAsync(CancellationToken cancellationToken)
    {
        try
        {
            // 确保签名证书存在
            await _certificateManager.EnsureSignCertificateExistsAsync();

            // 获取签名证书公钥（带缓存）
            var signCertPublicKey = await GetSignCertificatePublicKeyInternalAsync(cancellationToken);

            _logger.LogDebug("Sign certificate public key retrieved successfully");
            return signCertPublicKey;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get sign certificate public key");
            throw;
        }
    }

    /// <summary>
    /// 加载签名证书私钥（带缓存）
    /// </summary>
    private async Task<RSA> LoadSignCertificatePrivateKeyAsync(CancellationToken cancellationToken)
    {
        if (_options.EnableCaching)
        {
            var cachedKey = await _distributedCache.GetStringAsync(SignPrivateKeyCacheKey, cancellationToken);
            if (!string.IsNullOrEmpty(cachedKey))
            {
                var rsa = RSA.Create();
                rsa.ImportFromPem(cachedKey);
                return rsa;
            }
        }

        // 从文件系统加载
        var privateKey = await _certificateManager.LoadSignCertificatePrivateKeyAsync();

        // 缓存私钥（注意：这里缓存私钥需要特别小心安全性）
        if (_options.EnableCaching)
        {
            var privateKeyPem = privateKey.ExportRSAPrivateKeyPem();
            var cacheOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(_options.CacheDurationMinutes)
            };
            await _distributedCache.SetStringAsync(SignPrivateKeyCacheKey, privateKeyPem, cacheOptions, cancellationToken);
        }

        return privateKey;
    }

    /// <summary>
    /// 获取签名证书公钥（带缓存）- 内部方法
    /// </summary>
    private async Task<string> GetSignCertificatePublicKeyInternalAsync(CancellationToken cancellationToken)
    {
        if (_options.EnableCaching)
        {
            var cachedCert = await _distributedCache.GetStringAsync(SignCertCacheKey, cancellationToken);
            if (!string.IsNullOrEmpty(cachedCert))
            {
                return cachedCert;
            }
        }

        // 从文件系统加载
        var signCert     = await _certificateManager.LoadSignCertificateAsync();
        var publicKeyPem = Convert.ToBase64String(signCert.Export(X509ContentType.Cert));
        var pemFormatted = FormatAsPem(publicKeyPem, "CERTIFICATE");

        // 缓存证书
        if (_options.EnableCaching)
        {
            var cacheOptions = new DistributedCacheEntryOptions
            {
                AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(_options.CacheDurationMinutes)
            };
            await _distributedCache.SetStringAsync(SignCertCacheKey, pemFormatted, cacheOptions, cancellationToken);
        }

        return pemFormatted;
    }

    /// <summary>
    /// 清除证书缓存
    /// </summary>
    private async Task ClearCertificateCacheAsync(CancellationToken cancellationToken)
    {
        // 只清除签名证书缓存，根证书不缓存
        await _distributedCache.RemoveAsync(SignCertCacheKey,       cancellationToken);
        await _distributedCache.RemoveAsync(SignPrivateKeyCacheKey, cancellationToken);
        _logger.LogDebug("Sign certificate cache cleared");
    }

    /// <summary>
    /// 生成随机数
    /// </summary>
    private static string GenerateNonce()
    {
        var       nonceBytes = new byte[32];
        using var rng        = RandomNumberGenerator.Create();
        rng.GetBytes(nonceBytes);
        return Convert.ToBase64String(nonceBytes);
    }

    /// <summary>
    /// 格式化为PEM格式
    /// </summary>
    private static string FormatAsPem(string base64Content, string label)
    {
        var sb = new StringBuilder();
        sb.AppendLine($"-----BEGIN {label}-----");

        // 每64个字符换行
        for (int i = 0; i < base64Content.Length; i += 64)
        {
            var length = Math.Min(64, base64Content.Length - i);
            sb.AppendLine(base64Content.Substring(i, length));
        }

        sb.AppendLine($"-----END {label}-----");
        return sb.ToString();
    }
}
