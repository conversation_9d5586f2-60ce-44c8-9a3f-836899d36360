using DeviceGuardCloud.Application.Signatures.Models;
using DeviceGuardCloud.Application.Signatures.Queries;
using Microsoft.Extensions.Logging;
using Scrutor;
using System.Security.Cryptography;

namespace DeviceGuardCloud.Application.Signatures;

/// <summary>
/// 根证书查询实现
/// </summary>
[ServiceDescriptor]
public class RootCertificateQuery : IRootCertificateQuery
{
    private readonly ISignatureService _signatureService;
    private readonly ICertificateManager _certificateManager;
    private readonly ILogger<RootCertificateQuery> _logger;

    public RootCertificateQuery(
        ISignatureService signatureService,
        ICertificateManager certificateManager,
        ILogger<RootCertificateQuery> logger)
    {
        _signatureService = signatureService;
        _certificateManager = certificateManager;
        _logger = logger;
    }

    /// <summary>
    /// 获取根证书信息
    /// </summary>
    public async Task<RootCertificateResponseModel> GetRootCertificateAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing root certificate request");

        try
        {
            // 获取根证书公钥
            var publicKeyPem = await _signatureService.GetRootCertificatePublicKeyAsync(cancellationToken);

            // 加载根证书以获取详细信息
            var rootCert = await _certificateManager.LoadRootCertificateAsync();

            // 计算证书指纹
            var fingerprint = ComputeCertificateFingerprint(rootCert);

            var result = new RootCertificateResponseModel
            {
                PublicKey = publicKeyPem,
                Issuer = rootCert.Issuer,
                ValidFrom = rootCert.NotBefore,
                ValidTo = rootCert.NotAfter,
                Fingerprint = fingerprint
            };

            _logger.LogInformation("Root certificate request processed successfully");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process root certificate request");
            throw;
        }
    }

    /// <summary>
    /// 计算证书SHA256指纹
    /// </summary>
    private static string ComputeCertificateFingerprint(System.Security.Cryptography.X509Certificates.X509Certificate2 certificate)
    {
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(certificate.RawData);
        return Convert.ToHexString(hash).ToLowerInvariant();
    }
}
