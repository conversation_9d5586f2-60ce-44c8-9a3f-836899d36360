using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.Security.Queries;
using DeviceGuardCloud.Application.Signatures.Commands;
using Doulex.AspNetCore.Authorization;
using MediatR;
using Microsoft.Extensions.Logging;
using Scrutor;

namespace DeviceGuardCloud.Application.Signatures;

/// <summary>
/// 重新生成签名证书命令处理器
/// </summary>
[ServiceDescriptor]
public class RegenerateSignCertificateCommandHandler : IRequestHandler<RegenerateSignCertificateCommand>
{
    private readonly ISignatureService _signatureService;
    private readonly ILoginUserQuery _loginUserQuery;
    private readonly IAuditLogService _auditLogService;
    private readonly ILogger<RegenerateSignCertificateCommandHandler> _logger;

    public RegenerateSignCertificateCommandHandler(
        ISignatureService signatureService,
        ILoginUserQuery loginUserQuery,
        IAuditLogService auditLogService,
        ILogger<RegenerateSignCertificateCommandHandler> logger)
    {
        _signatureService = signatureService;
        _loginUserQuery = loginUserQuery;
        _auditLogService = auditLogService;
        _logger = logger;
    }

    /// <summary>
    /// 处理重新生成签名证书命令
    /// </summary>
    public async Task Handle(RegenerateSignCertificateCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing sign certificate regeneration request");

        try
        {
            // 验证管理员身份
            var loginAdmin = await _loginUserQuery.GetLoginAdminUserAsync(cancellationToken);
            if (loginAdmin == null)
            {
                throw new AuthorizeException("需要管理员权限");
            }

            // 执行签名证书重新生成（不涉及根证书）
            await _signatureService.RegenerateCertificateAsync(cancellationToken);

            // 记录审计日志（不保存管理员详细信息）
            await _auditLogService.LogAsync(new AuditLog
            {
                Operation = "SignCertificateRegeneration",
                EntityType = "Certificate",
                EntityId = "SignCertificate",
                EntityName = "Sign Certificate",
                Description = "管理员强制重新生成了数字签名证书",
                Details = new
                {
                    RegenerationTime = DateTime.UtcNow,
                    Reason = "Manual regeneration requested",
                    CertificateType = "SignCertificate"
                }
            }, cancellationToken);

            _logger.LogInformation("Sign certificate regeneration completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to regenerate sign certificate");

            // 记录失败的审计日志（不保存管理员详细信息）
            await _auditLogService.LogAsync(new AuditLog
            {
                Operation = "SignCertificateRegeneration",
                EntityType = "Certificate",
                EntityId = "SignCertificate",
                EntityName = "Sign Certificate",
                Description = $"签名证书重新生成失败: {ex.Message}",
                Details = new
                {
                    ErrorMessage = ex.Message,
                    ErrorType = ex.GetType().Name,
                    FailureTime = DateTime.UtcNow,
                    CertificateType = "SignCertificate"
                }
            }, cancellationToken);

            throw;
        }
    }
}
