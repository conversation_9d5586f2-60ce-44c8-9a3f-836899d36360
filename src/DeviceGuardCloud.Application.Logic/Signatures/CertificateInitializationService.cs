using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Sc<PERSON>tor;

namespace DeviceGuardCloud.Application.Signatures;

/// <summary>
/// 证书初始化后台服务，在应用程序启动时确保根证书存在
/// </summary>
[ServiceDescriptor]
public class CertificateInitializationService : IHostedService
{
    private readonly ICertificateManager _certificateManager;
    private readonly ILogger<CertificateInitializationService> _logger;

    public CertificateInitializationService(
        ICertificateManager certificateManager,
        ILogger<CertificateInitializationService> logger)
    {
        _certificateManager = certificateManager;
        _logger = logger;
    }

    /// <summary>
    /// 启动服务时初始化证书
    /// </summary>
    public async Task StartAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Certificate initialization service starting");

        try
        {
            // 确保根证书存在，如果不存在则生成
            await _certificateManager.EnsureRootCertificateExistsAsync();
            
            _logger.LogInformation("Root certificate initialization completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize root certificate during startup");
            
            // 注意：这里不抛出异常，因为我们不希望证书初始化失败导致整个应用程序启动失败
            // 证书仍然可以在运行时按需生成
            _logger.LogWarning("Certificate initialization failed, certificates will be generated on-demand");
        }
    }

    /// <summary>
    /// 停止服务
    /// </summary>
    public Task StopAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Certificate initialization service stopping");
        return Task.CompletedTask;
    }
}
