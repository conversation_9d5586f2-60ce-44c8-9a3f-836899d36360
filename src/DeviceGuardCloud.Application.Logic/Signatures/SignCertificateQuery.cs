using DeviceGuardCloud.Application.Signatures.Models;
using DeviceGuardCloud.Application.Signatures.Queries;
using Microsoft.Extensions.Logging;
using Scrutor;
using System.Security.Cryptography;

namespace DeviceGuardCloud.Application.Signatures;

/// <summary>
/// 签名证书查询实现
/// </summary>
[ServiceDescriptor]
public class SignCertificateQuery : ISignCertificateQuery
{
    private readonly ISignatureService _signatureService;
    private readonly ICertificateManager _certificateManager;
    private readonly ILogger<SignCertificateQuery> _logger;

    public SignCertificateQuery(
        ISignatureService signatureService,
        ICertificateManager certificateManager,
        ILogger<SignCertificateQuery> logger)
    {
        _signatureService = signatureService;
        _certificateManager = certificateManager;
        _logger = logger;
    }

    /// <summary>
    /// 获取当前签名证书信息
    /// </summary>
    public async Task<SignCertificateResponseModel> GetSignCertificateAsync(CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing sign certificate request");

        try
        {
            // 获取签名证书公钥
            var publicKeyPem = await _signatureService.GetSignCertificatePublicKeyAsync(cancellationToken);

            // 加载签名证书以获取详细信息
            var signCert = await _certificateManager.LoadSignCertificateAsync();

            // 计算证书指纹
            var fingerprint = ComputeCertificateFingerprint(signCert);

            var result = new SignCertificateResponseModel
            {
                PublicKey = publicKeyPem,
                Issuer = signCert.Issuer,
                Subject = signCert.Subject,
                ValidFrom = signCert.NotBefore,
                ValidTo = signCert.NotAfter,
                Fingerprint = fingerprint,
                SerialNumber = signCert.SerialNumber ?? "Unknown"
            };

            _logger.LogInformation("Sign certificate request processed successfully");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process sign certificate request");
            throw;
        }
    }

    /// <summary>
    /// 计算证书SHA256指纹
    /// </summary>
    private static string ComputeCertificateFingerprint(System.Security.Cryptography.X509Certificates.X509Certificate2 certificate)
    {
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(certificate.RawData);
        return Convert.ToHexString(hash).ToLowerInvariant();
    }
}
