using AspNetCore.Abstractions.Exceptions;
using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.Bundles.Deploys;
using DeviceGuardCloud.Application.PluginCategories.Queries;
using DeviceGuardCloud.Application.Plugins.Commands;
using DeviceGuardCloud.DomainModel.Bundles;
using DeviceGuardCloud.DomainModel.Plugins;
using DeviceGuardCloud.DomainModel.UserPlugins;
using Doulex.DomainDriven;
using LinqAsync;
using MediatR;

namespace DeviceGuardCloud.Application.Plugins;

public class RemovePluginCommandHandler : IRequestHandler<RemovePluginCommand>
{
    private readonly IBundleRepository        _bundleRepository;
    private readonly IUnitOfWork              _unitOfWork;
    private readonly IAuditLogService         _auditLogService;
    private readonly IBundleFileService       _bundleFileService;
    private readonly IPluginRepository        _pluginRepository;
    private readonly IUserPluginRepository    _userPluginRepository;
    private readonly IPluginDbQuery _pluginDbQuery;


    public RemovePluginCommandHandler(
        IBundleRepository        bundleRepository,
        IAuditLogService         auditLogService,
        IUnitOfWork              unitOfWork,
        IBundleFileService       bundleFileService,
        IPluginRepository        pluginRepository,
        IUserPluginRepository    userPluginRepository,
        IPluginDbQuery pluginCategoryTreeQuery)
    {
        _bundleRepository        = bundleRepository;
        _auditLogService         = auditLogService;
        _unitOfWork              = unitOfWork;
        _bundleFileService       = bundleFileService;
        _pluginRepository        = pluginRepository;
        _userPluginRepository    = userPluginRepository;
        _pluginDbQuery = pluginCategoryTreeQuery;
    }

    public async Task Handle(RemovePluginCommand request, CancellationToken cancellationToken)
    {
        // 1. 读取数据库记录
        var plugin = await _pluginRepository.GetAsync(request.Id, cancellationToken);
        if (plugin == null)
        {
            throw new BadRequestException("Plugin not found", nameof(request.Id));
        }

        // 如果已被 UserPlugins 关联使用, 则不允许删除
        if (await _userPluginRepository.AsQueryable()
                                       .Where(x => x.PluginId == plugin.Id)
                                       .AnyAsync(cancellationToken))
        {
            throw new BadRequestException("Plugin is used by UserPlugins", nameof(request.Id));
        }

        await using var tran = await _unitOfWork.BeginTransactionAsync(cancellationToken);

        // 2. 删除 bundles
        await _bundleRepository.RemoveRangeByPluginIdAsync(plugin.Id, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // 删除 Plugin
        await _pluginRepository.RemoveAsync(plugin, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // 删除文件 (注意: 删除文件没有事务)
        await _bundleFileService.RemoveAllAsync(plugin.Code, cancellationToken);

        // 4. 记录审计日志
        await _auditLogService.LogAsync(
            new AuditLog
            {
                EntityType  = nameof(Plugin),
                EntityId    = plugin.Id,
                EntityName  = plugin.Name,
                Operation   = AuditLog.Remove,
                Description = $"删除插件 '{plugin.Name}'",
            },
            cancellationToken);

        // 4. 提交数据库事务
        await tran.CommitAsync(cancellationToken);

        // 复位缓存
        _pluginDbQuery.ResetCache();
    }
}
