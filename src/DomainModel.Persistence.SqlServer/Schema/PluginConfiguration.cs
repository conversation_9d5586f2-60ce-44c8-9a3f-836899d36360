using DeviceGuardCloud.DomainModel.Plugins;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DeviceGuardCloud.DomainModel.Persistence.SqlServer.Schema;

/// <summary>
/// There are no comments for <see cref="Plugin"/> Configuration in the schema.
/// </summary>
public class PluginConfiguration : IEntityTypeConfiguration<Plugin>
{
    /// <summary>
    /// There are no comments
    /// </summary>
    public void Configure(EntityTypeBuilder<Plugin> builder)
    {
        builder.ToTable(@"Plugins", @"dbo");

        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).IsRequired().ValueGeneratedNever();
        builder.Property(x => x.RowVersion).IsRowVersion();

        builder.HasIndex(x => x.Code);
    }
}
