using DeviceGuardCloud.DomainModel.Boards;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DeviceGuardCloud.DomainModel.Persistence.SqlServer.Schema;

/// <summary>
/// There are no comments for <see cref="Board"/> Configuration in the schema.
/// </summary>
public class BoardConfiguration : IEntityTypeConfiguration<Board>
{
    /// <summary>
    /// There are no comments
    /// </summary>
    public void Configure(EntityTypeBuilder<Board> builder)
    {
        builder.ToTable(@"Boards", @"dbo");

        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).IsRequired().ValueGeneratedNever();

        builder.HasIndex(x => x.Model);
        builder.HasIndex(x => x.SerialNumber);
        builder.HasIndex(x => x.AgencyName);
        builder.HasIndex(x => x.OwnedBy);
    }
}
