using DeviceGuardCloud.DomainModel.UserPluginFetchAudits;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DeviceGuardCloud.DomainModel.Persistence.SqlServer.Schema;

/// <summary>
/// There are no comments for UserPluginFetchAuditConfiguration in the schema.
/// </summary>
public class UserPluginFetchAuditConfiguration : IEntityTypeConfiguration<UserPluginFetchAudit>
{
    /// <summary>
    /// There are no comments for Configure method in the schema.
    /// </summary>
    public void Configure(EntityTypeBuilder<UserPluginFetchAudit> builder)
    {
        builder.ToTable(@"UserPluginFetchAudits");

        builder.Property(x => x.Id);
        builder.Property(x => x.Id).IsRequired().ValueGeneratedNever();
        builder.Property(x => x.RowVersion).IsRowVersion();

        builder.HasKey(x => x.Id);

        builder.HasIndex(x => x.UserId);
        builder.HasIndex(x => x.MachineSerialNumber);
        builder.HasIndex(x => new { x.UserId, x.MachineSerialNumber }).IsUnique();
        builder.HasIndex(x => x.CreatedAt);
        builder.HasIndex(x => x.UpdatedAt);

        builder.Property(x => x.MachineSerialNumber).HasMaxLength(255).IsRequired();
    }
}
