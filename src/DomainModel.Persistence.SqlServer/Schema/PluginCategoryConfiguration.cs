using DeviceGuardCloud.DomainModel.PluginCategories;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DeviceGuardCloud.DomainModel.Persistence.SqlServer.Schema;

/// <summary>
/// There are no comments for <see cref="PluginCategory"/> Configuration in the schema.
/// </summary>
public class PluginPluginCategoryConfiguration : IEntityTypeConfiguration<PluginCategory>
{
    /// <summary>
    /// There are no comments
    /// </summary>
    public void Configure(EntityTypeBuilder<PluginCategory> pluginCategoryer)
    {
        pluginCategoryer.ToTable(@"PluginCategories", @"dbo");

        pluginCategoryer.HasKey(x => x.Id);
        pluginCategoryer.Property(x => x.Id).IsRequired().ValueGeneratedNever();
        pluginCategoryer.Property(x => x.RowVersion).IsRowVersion();

        pluginCategoryer.HasIndex(x => x.ParentId);
    }
}
