using DeviceGuardCloud.DomainModel.Modules;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DeviceGuardCloud.DomainModel.Persistence.SqlServer.Schema;

/// <summary>
/// There are no comments for <see cref="Module"/> Configuration in the schema.
/// </summary>
public class ModuleConfiguration : IEntityTypeConfiguration<Module>
{
    /// <summary>
    /// There are no comments
    /// </summary>
    public void Configure(EntityTypeBuilder<Module> builder)
    {
        builder.ToTable(@"Modules", @"dbo");

        builder.HasKey(x => x.Id);
        builder.Property(x => x.Id).IsRequired().ValueGeneratedNever();
        builder.Property(x => x.RowVersion).IsRowVersion();

        builder.HasIndex(x => x.Code);
        builder.HasIndex(x => x.Host);
    }
}
