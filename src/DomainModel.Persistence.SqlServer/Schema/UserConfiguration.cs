using DeviceGuardCloud.DomainModel.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DeviceGuardCloud.DomainModel.Persistence.SqlServer.Schema
{
    /// <summary>
    /// There are no comments for UserConfiguration in the schema.
    /// </summary>
    public class UserConfiguration : IEntityTypeConfiguration<User>
    {
        /// <summary>
        /// There are no comments for Configure method in the schema.
        /// </summary>
        public void Configure(EntityTypeBuilder<User> builder)
        {
            builder.ToTable(@"Users");

            builder.Property(x => x.Id);
            builder.Property(x => x.Id).IsRequired().ValueGeneratedNever();
            builder.Property(x => x.RowVersion).IsRowVersion();

            builder.HasKey(x => x.Id);
        }
    }
}
