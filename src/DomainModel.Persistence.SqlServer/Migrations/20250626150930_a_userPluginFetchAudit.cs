using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DeviceGuardCloud.DomainModel.Persistence.SqlServer.Migrations
{
    /// <inheritdoc />
    public partial class a_userPluginFetchAudit : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "UserPluginFetchAudits",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    UserId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MachineSerialNumber = table.Column<string>(type: "nvarchar(255)", maxLength: 255, nullable: false),
                    Count = table.Column<long>(type: "bigint", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    RowVersion = table.Column<byte[]>(type: "rowversion", rowVersion: true, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserPluginFetchAudits", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_UserPluginFetchAudits_CreatedAt",
                table: "UserPluginFetchAudits",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_UserPluginFetchAudits_MachineSerialNumber",
                table: "UserPluginFetchAudits",
                column: "MachineSerialNumber");

            migrationBuilder.CreateIndex(
                name: "IX_UserPluginFetchAudits_UpdatedAt",
                table: "UserPluginFetchAudits",
                column: "UpdatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_UserPluginFetchAudits_UserId",
                table: "UserPluginFetchAudits",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_UserPluginFetchAudits_UserId_MachineSerialNumber",
                table: "UserPluginFetchAudits",
                columns: new[] { "UserId", "MachineSerialNumber" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "UserPluginFetchAudits");
        }
    }
}
