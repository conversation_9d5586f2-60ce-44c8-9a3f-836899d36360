using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DeviceGuardCloud.DomainModel.Persistence.SqlServer.Migrations
{
    /// <inheritdoc />
    public partial class u_plugin_a_isFree : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "<PERSON><PERSON><PERSON>",
                schema: "dbo",
                table: "Plugins",
                type: "bit",
                nullable: false,
                defaultValue: false);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON>",
                schema: "dbo",
                table: "Plugins");
        }
    }
}
