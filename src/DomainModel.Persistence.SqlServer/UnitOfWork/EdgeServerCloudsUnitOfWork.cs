using Doulex.DomainDriven;
using Doulex.DomainDriven.Repo.EFCore;
using Scrutor;

namespace DeviceGuardCloud.DomainModel.Persistence.SqlServer.UnitOfWork;

[ServiceDescriptor]
public class DeviceGuardCloudUnitOfWork : EntityFrameworkCoreUnitOfWork, IUnitOfWork
{
    public DeviceGuardCloudUnitOfWork(DeviceGuardCloudDbContext dbContext) : base(dbContext)
    {
    }

    /// <summary>
    /// 支持嵌套事务
    /// TODO Try IT
    /// </summary>
    public override bool SupportNestedTransaction => true;
}
