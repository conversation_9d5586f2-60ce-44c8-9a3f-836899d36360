using Doulex.DomainDriven.Repo.EFCore;
using DeviceGuardCloud.DomainModel.AdminUsers;
using <PERSON><PERSON><PERSON>;

namespace DeviceGuardCloud.DomainModel.Persistence.SqlServer.Repository;

/// <summary>
/// 管理员账户的仓储
/// </summary>
[ServiceDescriptor]
public class AdminUserRepository : EntityFrameworkCoreRepository<AdminUser, Guid>, IAdminUserRepository
{
    /// <summary>
    /// 构造函数
    /// </summary>
    public AdminUserRepository(DeviceGuardCloudDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Find the admin records by associated userId
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public Task<AdminUser?> FindByUserIdAsync(Guid userId, CancellationToken cancel)
    {
        // UserId 就是主键
        return GetAsync(userId, cancel);
    }
}
