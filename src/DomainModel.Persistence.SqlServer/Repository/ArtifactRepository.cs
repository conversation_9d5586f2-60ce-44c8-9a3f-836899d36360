using DeviceGuardCloud.DomainModel.Artifacts;
using Doulex.DomainDriven.Repo.EFCore;
using Scrutor;

namespace DeviceGuardCloud.DomainModel.Persistence.SqlServer.Repository;

/// <summary>
/// The repository of <see cref="Artifact"/>
/// </summary>
[ServiceDescriptor]
public class ArtifactRepository : EntityFrameworkCoreRepository<Artifact, Guid>, IArtifactRepository
{
    public ArtifactRepository(DeviceGuardCloudDbContext context)
        : base(context)
    {
    }
}
