using DeviceGuardCloud.DomainModel.PluginCategories;
using Doulex.DomainDriven.Repo.EFCore;
using Scrutor;

namespace DeviceGuardCloud.DomainModel.Persistence.SqlServer.Repository;

/// <summary>
/// The repository of <see cref="PluginCategory"/>
/// </summary>
[ServiceDescriptor]
public class PluginPluginCategoryRepository : EntityFrameworkCoreRepository<PluginCategory, Guid>, IPluginCategoryRepository
{
    public PluginPluginCategoryRepository(DeviceGuardCloudDbContext context)
        : base(context)
    {
    }
}
