using Doulex.DomainDriven.Repo.EFCore;
using DeviceGuardCloud.DomainModel.Users;
using Microsoft.EntityFrameworkCore;
using Scrutor;

namespace DeviceGuardCloud.DomainModel.Persistence.SqlServer.Repository;

/// <summary>
/// The repository of <see cref="User"/>
/// </summary>
[ServiceDescriptor]
public class UserRepository : EntityFrameworkCoreRepository<User, Guid>, IUserRepository
{
    private readonly DbSet<User> _users;

    public UserRepository(DeviceGuardCloudDbContext context) : base(context)
    {
        _users = context.Set<User>();
    }


    /// <summary>
    /// Get the user by OAuth2 id, this id is come form "sub" claim in the token
    /// </summary>
    /// <param name="providerName"></param>
    /// <param name="providerValue"></param>
    /// <param name="cancel"></param>
    /// <returns></returns>
    public Task<User?> GetByOAuthIdAsync(string providerName, string providerValue, CancellationToken cancel)
    {
        return (from u in _users
                where u.ExternalLogins.Any(e => e.ProviderName == providerName && e.ProviderValue == providerValue)
                select u).FirstOrDefaultAsync(cancel);
    }
}
