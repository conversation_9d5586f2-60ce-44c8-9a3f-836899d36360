using System.Buffers.Binary;
using ChargerController;
using ChargerController.IO;
using ChargerController.Utils;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Windows.Utility.Dialogs;

namespace InfyPower.IO;

/// <summary>
/// 英飞源的充电模块
/// </summary>
public class InfyPowerClient
{
    /// <summary>
    /// 连接检测仪的客户端
    /// </summary>
    private readonly IChargerControllerClient _controller;

    /// <summary>
    /// 连接云端许可证的客户端
    /// </summary>
    private readonly ILicenseClient _licenseClient;

    public InfyPowerClient(IChargerControllerClient controller, ILicenseClient licenseClient)
    {
        _controller    = controller;
        _licenseClient = licenseClient;
    }

    /// <summary>
    /// 传输对象
    /// </summary>
    public ITransport? Transport { get; private set; }

    public ChargerPropertyValue<double?>   OutputVoltage  { get; } = new();
    public ChargerPropertyValue<double?>   OutputCurrent  { get; } = new();
    public ChargerPropertyValue<double?>   OutputPower    { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageAB { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageBC { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageCA { get; } = new();
    public ChargerPropertyValue<byte[]>   Status         { get; } = new();
    public ChargerPropertyValue<string[]> StatusTexts    { get; } = new();
    public ChargerPropertyValue<bool>     Enabled        { get; } = new();

    /// <summary>
    /// 端口状态
    /// </summary>
    public bool IsConnected => _controller.IsOpen;

    public string PortName { get; private set; } = "";

    /// <summary>
    /// 初始化底层, 包括打开端口, 订阅数据等
    /// </summary>
    /// <param name="transport"></param>
    /// <param name="scanInterval"></param>
    /// <param name="cancellationToken"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public async Task ConnectAsync(ITransport transport, uint scanInterval, CancellationToken cancellationToken)
    {
        if (transport == null) throw new ArgumentNullException(nameof(transport));

        // 创建串口客户端
        _controller.Use(transport);
        PortName  = transport.Name;
        Transport = transport;

        try
        {
             if (!await _controller.IsActivatedAsync(cancellationToken))
            {
                // 获取序列号
                var serialNumber = await _controller.GetSerialNumberAsync(cancellationToken);

                // 去云端激活
                var productKey = await _licenseClient.ActivateLicenseAsync(_controller.Sku, serialNumber, cancellationToken);

                // 把激活码写入设备
                await _controller.ActivateAsync(productKey, cancellationToken);
                throw new ReconnectException("激活完成, 请稍等测试仪重启动");
            }

            // 清理缓存
            await _controller.CleanAsync(cancellationToken);

            // 设置关闭模块的遗嘱
            await _controller.SetupWillDataAsync(0,
                CanType.Extended,
                0x029A3FF0,
                [0x01],
                1000,
                10,
                cancellationToken);

            // 读取模块0输入电压
            var subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                0x028600F0,
                [],
                scanInterval,
                0x0286F000,
                [],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    InputVoltageAB.Value = BinaryPrimitives.ReadUInt16BigEndian(data.AsSpan(0, 2)) / 10.0;
                    InputVoltageBC.Value = BinaryPrimitives.ReadUInt16BigEndian(data.AsSpan(2, 2)) / 10.0;
                    InputVoltageCA.Value = BinaryPrimitives.ReadUInt16BigEndian(data.AsSpan(4, 2)) / 10.0;
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块0三项输入电压失败");
            }

            // 读取模块0输出电压电流 
            subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                0x028900F0,
                [],
                scanInterval,
                0x0289F000,
                [],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    OutputVoltage.Value = BinaryPrimitives.ReadUInt32BigEndian(data.AsSpan(0, 4)) / 1000.0;
                    OutputCurrent.Value = BinaryPrimitives.ReadUInt32BigEndian(data.AsSpan(4, 4)) / 1000.0;
                    OutputPower.Value   = OutputVoltage.Value * OutputCurrent.Value;
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块0输出电压电流失败");
            }

            // 读取模块0状态
            subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                0x028400F0,
                [],
                scanInterval,
                0x0284F000,
                [],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    Status.Value  = data.Skip(5).ToArray();
                    Enabled.Value = (data[6] & 0x01) == 0x00;

                    // https://kmsmg.coding.net/p/zhiliuzhuangceshiyi/files/all/45723263/preview/44835769
                    StatusTexts.Value = new List<string>()
                                        .AddWhen((data[5] & 0x01) > 0, "模块处于限功率状态")
                                        .AddWhen((data[5] & 0x02) > 0, "模块ID重复")
                                        .AddWhen((data[5] & 0x04) > 0, "模块严重不均流")
                                        .AddWhen((data[5] & 0x08) > 0, "三相输入缺相告警")
                                        .AddWhen((data[5] & 0x10) > 0, "三相输入不平衡告警")
                                        .AddWhen((data[5] & 0x20) > 0, "输入欠压告警")
                                        .AddWhen((data[5] & 0x40) > 0, "输入过压告警")
                                        .AddWhen((data[5] & 0x80) > 0, "模块 PFC 侧处于关机状态")
                                        .AddWhen((data[6] & 0x01) > 0, "模块 DC 侧处于关机状态")
                                        .AddWhen((data[6] & 0x02) > 0, "模块故障告警")
                                        .AddWhen((data[6] & 0x04) > 0, "模块保护告警")
                                        .AddWhen((data[6] & 0x08) > 0, "风扇故障告警")
                                        .AddWhen((data[6] & 0x10) > 0, "过温告警")
                                        .AddWhen((data[6] & 0x20) > 0, "输出过压告警")
                                        .AddWhen((data[6] & 0x40) > 0, "WALK-IN 使能")
                                        .AddWhen((data[6] & 0x80) > 0, "模块通信中断告警")
                                        .AddWhen((data[7] & 0x01) > 0, "输出短路")
                                        .AddWhen((data[7] & 0x04) > 0, "模块内部通信故障")
                                        .AddWhen((data[7] & 0x08) > 0, "输入或母线异常")
                                        .AddWhen((data[7] & 0x10) > 0, "模块休眠")
                                        .AddWhen((data[7] & 0x20) > 0, "模块放电异常")
                                        .ToArray();
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块0状态失败");
            }
        }
        catch (Exception)
        {
            _controller.Close();
            throw;
        }
    }

    public async Task DisconnectAsync(CancellationToken cancellationToken)
    {
        try
        {
            await _controller.CleanAsync(cancellationToken);
        }
        catch (Exception)
        {
            // todo 发出警告, 但是忽略异常
        }
        finally
        {
            _controller.Close();
        }
    }

    public async Task SetupAsync(double voltage, double current, CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        uint voltageSetup = (uint)(voltage * 1000);
        uint currentSetup = (uint)(current * 1000);

        var payload = new byte[8];
        BinaryPrimitives.WriteUInt32BigEndian(payload,           voltageSetup);
        BinaryPrimitives.WriteUInt32BigEndian(payload.AsSpan(4), currentSetup);

        // 设置电压和电流 TODO 目前没有处理回应数据
        await _controller.SendAsync(CanType.Extended, 0x029C00F0, payload, cancellationToken);
    }

    public async Task OpenAsync(CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        // 启动 TODO 目前没有处理回应数据
        var result = await _controller.SendAsync(CanType.Extended, 0x029A3FF0, [0x00], cancellationToken);
        if (!result)
        {
            throw new Exception("启动失败");
        }
    }

    public async Task CloseAsync(CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        var result = await _controller.SendAsync(CanType.Extended, 0x029A3FF0, [0x01], cancellationToken);
        if (!result)
        {
            throw new Exception("停止失败");
        }
    }
}
