# 设备卫士云端管理系统 (Device Guard Cloud)

设备卫士云端管理系统（Device Guard Cloud，简称 DGC）是一款用于管理设备卫士（Device Guard，简称 DG）客户端的系统。

## 项目概述

本项目是一个基于.NET 8.0的Web API应用，用于管理和监控设备卫士客户端。系统提供了用户管理、设备管理、报警监控等功能，并支持与微信集成进行消息通知。

### 主要功能

- 用户认证与授权管理
- 管理员角色与权限控制
- 设备标签读取与管理
- 报警监控与通知
- 数据统计与趋势分析
- 微信消息通知集成
- API接口文档(Swagger)

## 技术栈

- .NET 8.0
- ASP.NET Core
- Entity Framework Core
- SQL Server
- Redis
- InfluxDB (时序数据库)
- Docker & Docker Compose
- Serilog (日志系统)
- MediatR (CQRS模式)
- Keycloak (认证服务)

## 开发环境设置

### 前提条件

- .NET 8.0 SDK
- Docker Desktop
- SQL Server (本地或Docker)
- Redis (本地或Docker)
- InfluxDB (本地或Docker)

### 本地开发环境启动

1. 克隆仓库
2. 使用Docker Compose启动依赖服务：

```bash
cd docs/Docs/esc-dev
docker-compose up -d
```

3. 在Visual Studio或其他IDE中打开解决方案
4. 设置`DeviceGuardCloud`为启动项目
5. 运行项目，默认会启动Swagger UI

## 版本管理

项目使用语义化版本控制(SemVer)：

- 主版本号：当做了不兼容的API修改或整个项目重大更新时增加
- 次版本号：当做了向下兼容的功能性新增时增加
- 修订号：当做了向下兼容的问题修正时增加
- 编译号：每次编译时自动增加

版本信息和更新日志存储在`src/DeviceGuardCloud/Properties/Changelog.json`文件中。

## 构建与发布

### 构建项目

```bash
cd src/DeviceGuardCloud
dotnet publish -c Release -o .\bin\publish
```

### 发布Docker镜像

使用提供的发布脚本：

```bash
cd src/DeviceGuardCloud
.\Publish.bat
```

该脚本会自动执行以下操作：
1. 清理旧的发布文件
2. 构建项目
3. 读取版本信息
4. 构建Docker镜像
5. 推送镜像到远程仓库

## 部署

### 使用Docker Compose部署

1. 准备`docker-compose.yml`文件（参考`docs/Docs/edge-server-clouds/docker-compose.yml`）
2. 配置环境变量或修改`appsettings.yml`
3. 启动服务：

```bash
docker-compose up -d
```

### 主要配置项

- 数据库连接字符串
- Redis缓存设置
- InfluxDB连接设置
- 日志配置
- 认证服务配置
- 微信API配置（可选）

## 证书

项目使用SSL证书进行HTTPS通信，证书文件存放在`src/DeviceGuardCloud/certs/`目录下。

## 项目结构

- `src/AspNetCore.Abstractions`: 定义ASP.NET Core公共抽象接口
- `src/DeviceGuardCloud`: 主项目，包含Web API和应用程序入口
- `src/DomainModel`: 领域模型定义
- `src/DomainModel.Persistence.SqlServer`: SQL Server数据持久化实现
- `src/DeviceGuardCloud.Application.Abstractions`: 应用层抽象接口
- `src/DeviceGuardCloud.Application.Logic`: 应用层业务逻辑实现
- `src/DeviceGuardCloud.Application.Queries`: 应用层查询实现
- `src/IntegrationEventBus`: 集成事件总线
- `docs`: 项目文档和部署配置示例