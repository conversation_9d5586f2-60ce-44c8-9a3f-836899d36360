version: '3'
services:
  portainer:
    container_name: portainer
    restart: always
    image: hendera-docker.pkg.coding.net/repo/docker/portainer/portainer-ce:2.19.1
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./data/portainer:/data
    ports:
      - "9000:9000"

  redis:
    container_name: redis
    restart: always
    image: hendera-docker.pkg.coding.net/repo/docker/redis:7.0.11
    volumes:
      - ./data/redis:/data

  sqlserver:
    container_name: sqlserver
    restart: always # restart automatic
    environment:
      MSSQL_SA_PASSWORD: GIU*&^tfoi4KHFY.ikhera
      ACCEPT_EULA: Y
    image: mcr.microsoft.com/mssql/server:2019-latest
    ports:
      - 1433:1433
    volumes:
      - ./data/sqlserver/db:/var/opt/mssql/data
      - ./data/sqlserver/secrets:/var/opt/mssql/secrets
      - ./log/sqlserver:/var/opt/mssql/log
  
  seq:
    container_name: seq
    restart: always
    image: hendera-docker.pkg.coding.net/repo/docker/datalust/seq:2023.2
    ports:
      - 5341:5341
      - 8010:80
    volumes:
      - ./data/seq/data:/data
      - ./data/seq/logs:/logs
    environment:
      - ACCEPT_EULA=Y
      - SEQ_FIRSTRUN_ADMINPASSWORDHASH=FHc5tgzLhd6KW6YBx1dcwTYZoMBRUIw9FEerzGcg6oS9tdC+7A==   # password is oulida123

  influxdb:
    container_name: influxdb
    restart: always
    image: hendera-docker.pkg.coding.net/repo/docker/influxdb:2.7.5
    ports:
      - "8086:8086"
    volumes:
      - ./data/influxdb/db:/var/lib/influxdb2
      - ./data/influxdb/etc:/etc/influxdb2

#  edge-server-clouds:
#    container_name: edge-server-clouds
#    restart: always
#    image: hendera-docker.pkg.coding.net/es/docker/edge-server-clouds:1.0.0-d1
#    ports:
#      - "80:8080"
#    environment:
#      - TZ=Asia/Shanghai
#      # Database
#      - Repository__ConnectionStrings__SqlServer=server=sqlserver;database=DeviceGuardCloud;user=sa;password=GIU*&^tfoi4KHFY.ikhera
#      # Redis
#      - DistributedCache__Provider=redis  # in-memory
#      - DistributedCache__Redis__Server=redis
#      # Serilog
#      - Serilog__WriteTo__0__Args__serverUrl=http://seq:5341
#      # InfluxDB
#      - InfluxDB__HostUrl=http://influxdb:8086
#      - InfluxDB__Token=NQuRTswHFaZCmpRHYefm4T-bx00Z-Sb7HG6RZKXgBv1hYc08h_W1wWE49y8Bp8vt13yDV13e5DEI1do4zLf8rQ==
#      - InfluxDB__Organization=DeviceGuard
#      - InfluxDB__TrendsBucket=trends
#      - InfluxDB__StatsBucket=stats
#      - InfluxDB__MaxWriteBatchSize=1000
#    volumes:
#      - ./data/edge-server-clouds/logs:/app/logs
#      - ./data/edge-server-clouds/dashboards:/app/dashboards
#    depends_on:
#      - sqlserver
#      - redis
#      - seq
