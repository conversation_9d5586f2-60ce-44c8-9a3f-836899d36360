# 插件ZIP包制作手册

## 概述

本手册将指导您如何创建符合系统要求的插件ZIP包。插件包是一个包含插件所有必要文件的压缩文件，用于上传到服务器进行安装和分发。

## 插件包结构

### 标准目录结构
```
插件名-版本号.zip
└─  manifest.json
  ├─install/
  └─public/
      └─plugin.png (或 .jpg, .ico)
```

### 命名规范
- ZIP文件名格式：`{插件ID}-{版本号}.zip`
- 示例：`power.infy.reg1k0135a2-1.0.0.zip`

## 核心文件说明

### 1. manifest.json（必需）
这是插件的核心配置文件，必须位于根目录。

**文件内容示例：**
```json
{
  "id": "power.infy.reg1k0135a2",
  "version": "1.0.0",
  "module": {
    "id": "InfyPower",
    "version": "1.0.0"
  },
  "moduleParameter": "{}"
}
```

**字段说明：**
- `id`：插件的唯一标识符，建议使用反向域名格式
- `version`：插件版本号，使用SemVer语义化版本规范
- `module.id`：插件依赖的DLL模块ID
- `module.version`：依赖的DLL模块版本
- `moduleParameter`：传递给DLL模块的参数，JSON字符串格式

### 2. public/ 目录
存放插件的公开展示信息，类似于Linux包管理器中的软件包元数据。这些文件在用户浏览插件商店时就能访问，无需购买插件。

**必需文件：**
- `plugin.png`（或 `.jpg`、`.ico`）：插件图标文件

### 3. install/ 目录
存放用户购买插件后才能下载的实际安装文件，包含插件的核心功能配置。

**典型内容：**
- 插件的核心功能文件
- 配置文件模板
- 资源文件（图片、音频等）
- 安装后配置脚本

## 制作步骤

### 步骤1：准备工作目录
1. 创建一个新文件夹，命名为：`{插件ID}-{版本号}`
2. 在该文件夹下创建以下子目录：
    - `install/`
    - `public/`

### 步骤2：创建manifest.json
1. 在根目录创建 `manifest.json` 文件
2. 按照上述格式填写插件信息
3. 版本号必须符合SemVer规范（如：1.0.0、2.1.3、1.0.0-beta.1）
4. 确保JSON格式正确（可使用在线JSON验证器检查）

### 步骤3：准备公开展示文件
1. 在 `public/` 目录下放置以下文件：
    - **插件图标**：`plugin.png`（必需）
    - **插件介绍**：`README.md`（推荐）
    - **功能截图**：存放在 `screenshots/` 子目录（可选）

2. 图标文件建议规格：
    - 格式：PNG（推荐）、JPG或ICO
    - 尺寸：128x128像素（推荐）或64x64像素
    - 文件大小：不超过100KB
    - 背景：透明或白色

### 步骤4：准备安装文件
1. 将插件的核心功能文件放入 `install/` 目录
2. 组织文件结构，建议按功能分类：
   ```
   install/
   ├─config/       # 配置文件模板
   ├─resources/    # 资源文件
   ```
3. 确保所有必需的依赖文件都已包含
4. 测试文件的完整性和功能正确性

### 步骤5：打包压缩
1. 选择整个插件目录（不是目录内的文件）
2. 使用ZIP压缩工具创建压缩包
3. 确保压缩包内保持正确的目录结构
4. 压缩级别建议使用标准压缩（平衡文件大小和解压速度）
