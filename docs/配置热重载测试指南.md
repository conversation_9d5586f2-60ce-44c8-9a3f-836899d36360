# 配置热重载测试指南

本文档说明如何测试首页重定向配置的热重载功能。

## 前提条件

1. 确保应用程序正在运行
2. 确保配置文件路径正确：`src/DeviceGuardCloud/settings/appsettings.yml`

## 测试步骤

### 1. 启动应用程序

```bash
cd src/DeviceGuardCloud
dotnet run
```

### 2. 验证当前配置

访问测试端点来查看当前配置：
```
GET http://localhost:5000/api/v1/config-test/home-redirection
```

预期响应：
```json
{
  "enabled": true,
  "redirectUrl": "swagger/index.html",
  "type": "Temporary",
  "timestamp": "2024-01-01T12:00:00",
  "message": "这是当前的配置值，修改配置文件后应该立即生效"
}
```

### 3. 测试首页重定向

访问根路径：
```
GET http://localhost:5000/
```

应该重定向到 `swagger/index.html`

### 4. 修改配置文件

编辑 `src/DeviceGuardCloud/settings/appsettings.yml` 文件：

```yaml
HomePageRedirection:
  Enabled: true
  RedirectUrl: "admin/index.html"  # 修改重定向URL
  Type: "Permanent"                # 修改重定向类型
```

**注意：不要重启应用程序！**

### 5. 验证配置热重载

再次访问测试端点：
```
GET http://localhost:5000/api/v1/config-test/home-redirection
```

预期响应（配置应该已经更新）：
```json
{
  "enabled": true,
  "redirectUrl": "admin/index.html",  // 已更新
  "type": "Permanent",                // 已更新
  "timestamp": "2024-01-01T12:01:00",
  "message": "这是当前的配置值，修改配置文件后应该立即生效"
}
```

### 6. 验证重定向行为

再次访问根路径：
```
GET http://localhost:5000/
```

现在应该重定向到 `admin/index.html`，并且是永久重定向（HTTP 301）

### 7. 测试禁用重定向

修改配置文件：
```yaml
HomePageRedirection:
  Enabled: false  # 禁用重定向
  RedirectUrl: "admin/index.html"
  Type: "Permanent"
```

访问根路径：
```
GET http://localhost:5000/
```

应该返回 404 Not Found，消息为："Home page redirection is disabled."

## 故障排除

### 配置不生效的可能原因

1. **YAML 格式错误**
   - 检查缩进是否正确（使用空格，不是Tab）
   - 检查冒号后是否有空格
   - 验证 YAML 语法

2. **配置文件路径错误**
   - 确认文件路径：`src/DeviceGuardCloud/settings/appsettings.yml`
   - 检查文件是否存在

3. **配置节点名称错误**
   - 确保使用正确的节点名称：`HomePageRedirection`
   - 区分大小写

4. **文件监控未启用**
   - 检查 `Program.cs` 中的配置加载：
   ```csharp
   .AddYamlFile("./settings/appsettings.yml", false, true)  // 第三个参数应为 true
   ```

### 验证配置加载

使用以下 API 端点验证配置是否正确加载：

```
GET http://localhost:5000/api/v1/config-test/test-redirect
```

这个端点会返回当前配置信息，不会实际执行重定向。

## 技术实现说明

### IOptionsMonitor vs IOptions

- `IOptions<T>`: 应用程序启动时读取一次，不支持热重载
- `IOptionsMonitor<T>`: 支持配置变更监控，使用 `CurrentValue` 获取最新值

### 配置文件监控

配置文件监控通过以下方式启用：
```csharp
builder.Configuration
    .AddYamlFile("./settings/appsettings.yml", false, true)  // reloadOnChange: true
```

### 重定向类型

- `Temporary`: HTTP 302 临时重定向
- `Permanent`: HTTP 301 永久重定向

## 测试用例

可以使用以下测试用例验证功能：

1. **基本重定向测试**
2. **配置热重载测试**
3. **重定向类型测试**
4. **禁用重定向测试**
5. **外部URL重定向测试**

所有测试用例都在 `HomeControllerTests.cs` 中实现。
