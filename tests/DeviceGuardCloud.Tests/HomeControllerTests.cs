// using DeviceGuardCloud.WebApi.Controllers;
// using DeviceGuardCloud.WebApi.Options;
// using Microsoft.AspNetCore.Mvc;
// using Microsoft.Extensions.Options;
// using Moq;
//
// namespace DeviceGuardCloud.Application.Queries.Tests;
//
// public class HomeControllerTests
// {
//     [Fact]
//     public void Index_ShouldReturnTemporaryRedirect_WhenEnabledWithTemporaryType()
//     {
//         // Arrange
//         var options = new HomePageRedirectionOptions
//         {
//             Enabled = true,
//             RedirectUrl = "swagger/index.html",
//             Type = RedirectionType.Temporary
//         };
//         var mockOptions = new Mock<IOptionsSnapshot<HomePageRedirectionOptions>>();
//         mockOptions.Setup(x => x.Value).Returns(options);
//         
//         var controller = new HomeController(mockOptions.Object);
//
//         // Act
//         var result = controller.Index();
//
//         // Assert
//         var redirectResult = Assert.IsType<RedirectResult>(result);
//         Assert.Equal("swagger/index.html", redirectResult.Url);
//         Assert.False(redirectResult.Permanent);
//     }
//
//     [Fact]
//     public void Index_ShouldReturnPermanentRedirect_WhenEnabledWithPermanentType()
//     {
//         // Arrange
//         var options = new HomePageRedirectionOptions
//         {
//             Enabled = true,
//             RedirectUrl = "admin/index.html",
//             Type = RedirectionType.Permanent
//         };
//         var mockOptions = new Mock<IOptions<HomePageRedirectionOptions>>();
//         mockOptions.Setup(x => x.Value).Returns(options);
//         
//         var controller = new HomeController(mockOptions.Object);
//
//         // Act
//         var result = controller.Index();
//
//         // Assert
//         var redirectResult = Assert.IsType<RedirectResult>(result);
//         Assert.Equal("admin/index.html", redirectResult.Url);
//         Assert.True(redirectResult.Permanent);
//     }
//
//     [Fact]
//     public void Index_ShouldReturnNotFound_WhenDisabled()
//     {
//         // Arrange
//         var options = new HomePageRedirectionOptions
//         {
//             Enabled = false,
//             RedirectUrl = "swagger/index.html",
//             Type = RedirectionType.Temporary
//         };
//         var mockOptions = new Mock<IOptions<HomePageRedirectionOptions>>();
//         mockOptions.Setup(x => x.Value).Returns(options);
//         
//         var controller = new HomeController(mockOptions.Object);
//
//         // Act
//         var result = controller.Index();
//
//         // Assert
//         var notFoundResult = Assert.IsType<NotFoundObjectResult>(result);
//         Assert.Equal("Home page redirection is disabled.", notFoundResult.Value);
//     }
//
//     [Fact]
//     public void Index_ShouldRedirectToExternalUrl_WhenConfiguredWithExternalUrl()
//     {
//         // Arrange
//         var options = new HomePageRedirectionOptions
//         {
//             Enabled = true,
//             RedirectUrl = "https://example.com",
//             Type = RedirectionType.Temporary
//         };
//         var mockOptions = new Mock<IOptions<HomePageRedirectionOptions>>();
//         mockOptions.Setup(x => x.Value).Returns(options);
//         
//         var controller = new HomeController(mockOptions.Object);
//
//         // Act
//         var result = controller.Index();
//
//         // Assert
//         var redirectResult = Assert.IsType<RedirectResult>(result);
//         Assert.Equal("https://example.com", redirectResult.Url);
//         Assert.False(redirectResult.Permanent);
//     }
//
//     [Fact]
//     public void Index_ShouldUseDefaultValues_WhenOptionsAreDefault()
//     {
//         // Arrange
//         var options = new HomePageRedirectionOptions(); // 使用默认值
//         var mockOptions = new Mock<IOptions<HomePageRedirectionOptions>>();
//         mockOptions.Setup(x => x.Value).Returns(options);
//         
//         var controller = new HomeController(mockOptions.Object);
//
//         // Act
//         var result = controller.Index();
//
//         // Assert
//         var redirectResult = Assert.IsType<RedirectResult>(result);
//         Assert.Equal("swagger/index.html", redirectResult.Url); // 默认值
//         Assert.False(redirectResult.Permanent); // 默认是临时重定向
//     }
// }
