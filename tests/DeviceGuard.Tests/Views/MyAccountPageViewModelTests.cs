using DeviceGuard.Shell.Views.Pages.MyAccountPage;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Windows.Utility.Dialogs;
using Moq;
using Xunit;

namespace DeviceGuard.Tests.Views;

/// <summary>
/// MyAccountPageViewModel 的单元测试
/// </summary>
public class MyAccountPageViewModelTests
{
    private readonly Mock<ILoginSession> _mockLoginSession;
    private readonly Mock<IDialogService> _mockDialogService;
    private readonly MyAccountPageViewModel _viewModel;

    public MyAccountPageViewModelTests()
    {
        _mockLoginSession = new Mock<ILoginSession>();
        _mockDialogService = new Mock<IDialogService>();
        
        _viewModel = new MyAccountPageViewModel(_mockLoginSession.Object, _mockDialogService.Object);
    }

    [Fact]
    public void Constructor_ShouldInitializeMenuItems()
    {
        // Assert
        Assert.NotNull(_viewModel.MenuItems);
        Assert.Equal(7, _viewModel.MenuItems.Count);
        
        // 验证菜单项
        Assert.Equal("个人信息", _viewModel.MenuItems[0].Name);
        Assert.Equal("安全设置", _viewModel.MenuItems[1].Name);
        Assert.Equal("设备管理", _viewModel.MenuItems[2].Name);
        Assert.Equal("通知设置", _viewModel.MenuItems[3].Name);
        Assert.Equal("账户信息", _viewModel.MenuItems[4].Name);
        Assert.Equal("使用统计", _viewModel.MenuItems[5].Name);
        Assert.Equal("帮助中心", _viewModel.MenuItems[6].Name);
    }

    [Fact]
    public void Constructor_ShouldSetFirstMenuItemAsSelected()
    {
        // Assert
        Assert.True(_viewModel.MenuItems[0].IsSelected);
        Assert.Equal("PersonalInfo", _viewModel.CurrentPageType);
        
        // 其他菜单项应该未选中
        for (int i = 1; i < _viewModel.MenuItems.Count; i++)
        {
            Assert.False(_viewModel.MenuItems[i].IsSelected);
        }
    }

    [Fact]
    public void SelectMenuCommand_ShouldUpdateCurrentPageType()
    {
        // Arrange
        var securityMenuItem = _viewModel.MenuItems[1]; // 安全设置

        // Act
        _viewModel.SelectMenuCommand.Execute(securityMenuItem);

        // Assert
        Assert.Equal("SecuritySettings", _viewModel.CurrentPageType);
        Assert.True(securityMenuItem.IsSelected);
        Assert.False(_viewModel.MenuItems[0].IsSelected); // 个人信息应该取消选中
    }

    [Fact]
    public void SelectMenuCommand_ShouldUpdateOnlySelectedItem()
    {
        // Arrange
        var deviceMenuItem = _viewModel.MenuItems[2]; // 设备管理

        // Act
        _viewModel.SelectMenuCommand.Execute(deviceMenuItem);

        // Assert
        Assert.True(deviceMenuItem.IsSelected);
        
        // 其他所有菜单项应该未选中
        for (int i = 0; i < _viewModel.MenuItems.Count; i++)
        {
            if (i != 2)
            {
                Assert.False(_viewModel.MenuItems[i].IsSelected);
            }
        }
    }

    [Fact]
    public void IsLoggedIn_WhenSessionIsLoggedIn_ShouldReturnTrue()
    {
        // Arrange
        _mockLoginSession.Setup(x => x.IsLoggedIn).Returns(true);

        // Act & Assert
        Assert.True(_viewModel.IsLoggedIn);
    }

    [Fact]
    public void IsLoggedIn_WhenSessionIsNotLoggedIn_ShouldReturnFalse()
    {
        // Arrange
        _mockLoginSession.Setup(x => x.IsLoggedIn).Returns(false);

        // Act & Assert
        Assert.False(_viewModel.IsLoggedIn);
    }

    [Fact]
    public void UserName_ShouldReturnSessionUserName()
    {
        // Arrange
        const string expectedUserName = "TestUser";
        _mockLoginSession.Setup(x => x.UserName).Returns(expectedUserName);

        // Act & Assert
        Assert.Equal(expectedUserName, _viewModel.UserName);
    }
}
