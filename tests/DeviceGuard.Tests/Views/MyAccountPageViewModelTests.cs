using DeviceGuard.Shell.Views.Pages.MyAccountPage;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Windows.Utility.Dialogs;
using DeviceGuard.Shell.Views.LoginDialog;
using Moq;
using Xunit;
using MaterialDesignThemes.Wpf;

namespace DeviceGuard.Tests.Views;

/// <summary>
/// AccountMenuItem 的单元测试
/// </summary>
public class AccountMenuItemTests
{
    [Fact]
    public void AccountMenuItem_ShouldInitializeWithDefaultValues()
    {
        // Act
        var menuItem = new AccountMenuItem();

        // Assert
        Assert.Equal(string.Empty, menuItem.Name);
        Assert.Equal(PackIconKind.Abacus, menuItem.Icon); // 默认值是Abacus，不是None
        Assert.False(menuItem.IsSelected);
        Assert.Equal(string.Empty, menuItem.PageType);
    }

    [Fact]
    public void AccountMenuItem_ShouldSetPropertiesCorrectly()
    {
        // Arrange
        var menuItem = new AccountMenuItem();

        // Act
        menuItem.Name = "个人信息";
        menuItem.Icon = PackIconKind.Account;
        menuItem.IsSelected = true;
        menuItem.PageType = "PersonalInfo";

        // Assert
        Assert.Equal("个人信息", menuItem.Name);
        Assert.Equal(PackIconKind.Account, menuItem.Icon);
        Assert.True(menuItem.IsSelected);
        Assert.Equal("PersonalInfo", menuItem.PageType);
    }
}
