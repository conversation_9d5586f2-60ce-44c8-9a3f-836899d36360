using DeviceGuardCloud.Application.Signatures;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Newtonsoft.Json.Linq;
using System.Security.Cryptography.X509Certificates;

namespace DeviceGuardCloud.Application.Logic.Tests.Signatures;

public class SignatureServiceTests : IDisposable
{
    private readonly Mock<IOptions<SignatureOptions>> _mockOptions;
    private readonly Mock<ICertificateManager> _mockCertificateManager;
    private readonly Mock<IDistributedCache> _mockDistributedCache;
    private readonly Mock<ILogger<SignatureService>> _mockLogger;
    private readonly SignatureService _signatureService;
    private readonly SignatureOptions _signatureOptions;

    public SignatureServiceTests()
    {
        _signatureOptions = new SignatureOptions
        {
            RootCertificatePath = "test-certs/root-certificate.pem",
            RootPrivateKeyPath = "test-certs/root-private-key.pem",
            SignCertificatePath = "test-certs/sign-certificate.pem",
            SignPrivateKeyPath = "test-certs/sign-private-key.pem",
            CertificateValidityDays = 365,
            KeySize = 2048,
            EnableCaching = true,
            CacheDurationMinutes = 60,
            MaxSignatureRequestsPerMinute = 100,
            Subject = new CertificateSubjectOptions
            {
                Country = "CN",
                State = "Yunnan",
                City = "Kunming",
                Organization = "CFTech",
                OrganizationalUnit = "Security",
                CommonName = "CFTech Signature Authority"
            }
        };

        _mockOptions = new Mock<IOptions<SignatureOptions>>();
        _mockOptions.Setup(x => x.Value).Returns(_signatureOptions);

        _mockCertificateManager = new Mock<ICertificateManager>();
        _mockDistributedCache = new Mock<IDistributedCache>();
        _mockLogger = new Mock<ILogger<SignatureService>>();

        _signatureService = new SignatureService(
            _mockOptions.Object,
            _mockCertificateManager.Object,
            _mockDistributedCache.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task SignJsonAsync_ShouldReturnValidSignature_WhenValidDataProvided()
    {
        // Arrange
        var jsonData = JObject.FromObject(new { message = "test data", timestamp = DateTime.UtcNow });

        _mockCertificateManager.Setup(x => x.EnsureRootCertificateExistsAsync())
                              .Returns(Task.CompletedTask);
        _mockCertificateManager.Setup(x => x.EnsureSignCertificateExistsAsync())
                              .Returns(Task.CompletedTask);

        // Act & Assert
        // Note: This test would require actual certificate generation for full testing
        // For now, we verify the method structure and parameter validation
        var exception = await Record.ExceptionAsync(() =>
            _signatureService.SignJsonAsync(jsonData, CancellationToken.None));

        // The method should attempt to ensure certificates exist
        _mockCertificateManager.Verify(x => x.EnsureRootCertificateExistsAsync(), Times.Once);
        _mockCertificateManager.Verify(x => x.EnsureSignCertificateExistsAsync(), Times.Once);
    }

    [Fact]
    public async Task SignJsonAsync_ShouldThrowException_WhenJsonDataIsNull()
    {
        // Arrange
        JObject? jsonData = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentNullException>(() =>
            _signatureService.SignJsonAsync(jsonData!, CancellationToken.None));
    }

    [Fact]
    public async Task RegenerateCertificateAsync_ShouldCallCertificateManager_WhenCalled()
    {
        // Arrange
        _mockCertificateManager.Setup(x => x.RegenerateSignCertificateAsync())
                              .Returns(Task.CompletedTask);

        // Act
        await _signatureService.RegenerateCertificateAsync(CancellationToken.None);

        // Assert
        _mockCertificateManager.Verify(x => x.RegenerateSignCertificateAsync(), Times.Once);
    }

    [Fact]
    public async Task GetRootCertificatePublicKeyAsync_ShouldLoadFromFileSystem_WhenCertificateExists()
    {
        // Arrange - 根证书已在启动时确保存在，无需再次确保

        // Act & Assert
        var exception = await Record.ExceptionAsync(() =>
            _signatureService.GetRootCertificatePublicKeyAsync(CancellationToken.None));

        // Verify that it does NOT attempt to ensure certificate exists when certificate is available
        _mockCertificateManager.Verify(x => x.EnsureRootCertificateExistsAsync(), Times.Never);
    }

    [Fact]
    public async Task GetRootCertificatePublicKeyAsync_ShouldRegenerateCertificate_WhenFileNotFound()
    {
        // Arrange
        var mockCertificate = Mock.Of<X509Certificate2>();
        _mockCertificateManager.SetupSequence(x => x.LoadRootCertificateAsync())
                              .ThrowsAsync(new FileNotFoundException("Root certificate not found"))
                              .ReturnsAsync(mockCertificate); // Second call after regeneration succeeds

        _mockCertificateManager.Setup(x => x.EnsureRootCertificateExistsAsync())
                              .Returns(Task.CompletedTask);

        // Act & Assert
        var exception = await Record.ExceptionAsync(() =>
            _signatureService.GetRootCertificatePublicKeyAsync(CancellationToken.None));

        // Verify that it attempts to regenerate certificate when file is not found
        _mockCertificateManager.Verify(x => x.EnsureRootCertificateExistsAsync(), Times.Once);
        _mockCertificateManager.Verify(x => x.LoadRootCertificateAsync(), Times.Exactly(2));
    }

    [Fact]
    public async Task GetRootCertificatePublicKeyAsync_ShouldThrowException_WhenRegenerationFails()
    {
        // Arrange
        _mockCertificateManager.Setup(x => x.LoadRootCertificateAsync())
                              .ThrowsAsync(new FileNotFoundException("Root certificate not found"));

        _mockCertificateManager.Setup(x => x.EnsureRootCertificateExistsAsync())
                              .ThrowsAsync(new InvalidOperationException("Failed to generate certificate"));

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(() =>
            _signatureService.GetRootCertificatePublicKeyAsync(CancellationToken.None));

        // Verify that it attempts to regenerate certificate
        _mockCertificateManager.Verify(x => x.EnsureRootCertificateExistsAsync(), Times.Once);
    }

    [Theory]
    [InlineData(true)]
    [InlineData(false)]
    public void Constructor_ShouldSetCachingOption_BasedOnConfiguration(bool enableCaching)
    {
        // Arrange
        var options = new SignatureOptions { EnableCaching = enableCaching };
        var mockOptions = new Mock<IOptions<SignatureOptions>>();
        mockOptions.Setup(x => x.Value).Returns(options);

        // Act
        var service = new SignatureService(
            mockOptions.Object,
            _mockCertificateManager.Object,
            _mockDistributedCache.Object,
            _mockLogger.Object);

        // Assert
        Assert.NotNull(service);
        // The service should be created successfully regardless of caching setting
    }

    public void Dispose()
    {
        // Cleanup if needed
    }
}
