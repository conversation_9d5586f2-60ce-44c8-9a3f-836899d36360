using DeviceGuardCloud.Application.Signatures;
using Microsoft.Extensions.Logging;
using Moq;

namespace DeviceGuardCloud.Application.Logic.Tests.Signatures;

public class CertificateInitializationServiceTests
{
    private readonly Mock<ICertificateManager> _mockCertificateManager;
    private readonly Mock<ILogger<CertificateInitializationService>> _mockLogger;
    private readonly CertificateInitializationService _service;

    public CertificateInitializationServiceTests()
    {
        _mockCertificateManager = new Mock<ICertificateManager>();
        _mockLogger = new Mock<ILogger<CertificateInitializationService>>();
        _service = new CertificateInitializationService(_mockCertificateManager.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task StartAsync_ShouldEnsureRootCertificateExists_WhenCalled()
    {
        // Arrange
        _mockCertificateManager.Setup(x => x.EnsureRootCertificateExistsAsync())
                              .Returns(Task.CompletedTask);

        // Act
        await _service.StartAsync(CancellationToken.None);

        // Assert
        _mockCertificateManager.Verify(x => x.EnsureRootCertificateExistsAsync(), Times.Once);
    }

    [Fact]
    public async Task StartAsync_ShouldLogSuccess_WhenCertificateInitializationSucceeds()
    {
        // Arrange
        _mockCertificateManager.Setup(x => x.EnsureRootCertificateExistsAsync())
                              .Returns(Task.CompletedTask);

        // Act
        await _service.StartAsync(CancellationToken.None);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Certificate initialization service starting")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Root certificate initialization completed successfully")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task StartAsync_ShouldLogErrorAndNotThrow_WhenCertificateInitializationFails()
    {
        // Arrange
        var expectedException = new InvalidOperationException("Certificate generation failed");
        _mockCertificateManager.Setup(x => x.EnsureRootCertificateExistsAsync())
                              .ThrowsAsync(expectedException);

        // Act & Assert - Should not throw exception
        var exception = await Record.ExceptionAsync(() => _service.StartAsync(CancellationToken.None));
        Assert.Null(exception);

        // Verify error logging
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Error,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Failed to initialize root certificate during startup")),
                expectedException,
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);

        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Warning,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Certificate initialization failed, certificates will be generated on-demand")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task StopAsync_ShouldLogStop_WhenCalled()
    {
        // Act
        await _service.StopAsync(CancellationToken.None);

        // Assert
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Information,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Certificate initialization service stopping")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public async Task StopAsync_ShouldCompleteSuccessfully_Always()
    {
        // Act & Assert - Should not throw
        var exception = await Record.ExceptionAsync(() => _service.StopAsync(CancellationToken.None));
        Assert.Null(exception);
    }
}
