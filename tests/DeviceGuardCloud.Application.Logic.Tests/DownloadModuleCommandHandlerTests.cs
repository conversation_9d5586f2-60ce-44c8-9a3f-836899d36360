// using AspNetCore.Abstractions.Exceptions;
// using DeviceGuardCloud.Application.Builds.Deploys;
// using DeviceGuardCloud.Application.Modules;
// using DeviceGuardCloud.Application.Modules.Commands;
// using DeviceGuardCloud.Application.Security.Queries;
// using DeviceGuardCloud.Application.Security.Queries.Models;
// using DeviceGuardCloud.DomainModel.Builds;
// using DeviceGuardCloud.DomainModel.DownloadCounts;
// using DeviceGuardCloud.DomainModel.Modules;
//
// using Doulex.AspNetCore.Authorization;
// using LinqAsync;
// using Moq;
//
// namespace DeviceGuardCloud.Application.Logic.Tests;
//
// public class DownloadModuleCommandHandlerTests
// {
//     private readonly Mock<IModuleRepository>        _moduleRepositoryMock;
//     private readonly Mock<IBuildRepository>         _buildRepositoryMock;
//     private readonly Mock<IPackageFileService>      _packageFileServiceMock;
//     private readonly Mock<IDownloadCountRepository> _downloadCountRepositoryMock;
//     private readonly Mock<ILoginUserQuery>          _loginUserQueryMock;
//     private readonly Mock<IQueryable<Build>>        _buildQueryableMock;
//
//     private readonly DownloadModuleCommandHandler _handler;
//
//     public DownloadModuleCommandHandlerTests()
//     {
//         _moduleRepositoryMock        = new Mock<IModuleRepository>();
//         _buildRepositoryMock         = new Mock<IBuildRepository>();
//         _packageFileServiceMock      = new Mock<IPackageFileService>();
//         _downloadCountRepositoryMock = new Mock<IDownloadCountRepository>();
//         _loginUserQueryMock          = new Mock<ILoginUserQuery>();
//         _buildQueryableMock          = new Mock<IQueryable<Build>>();
//
//         _handler = new DownloadModuleCommandHandler(
//             _moduleRepositoryMock.Object,
//             _buildRepositoryMock.Object,
//             _packageFileServiceMock.Object,
//             _downloadCountRepositoryMock.Object,
//             _loginUserQueryMock.Object);
//     }
//
//     [Fact]
//     public async Task Handle_ValidModuleCodeWithoutVersion_ShouldDownloadLatestStableVersion()
//     {
//         // Arrange
//         var command = new DownloadModuleCommand
//         {
//             Code = "TestModule",
//             Version  = null
//         };
//
//         var loginUser = new LoginUser { UserId = Guid.NewGuid() };
//         var module = new Module
//         {
//             Platform  = PlatformType.WindowsDesktop,
//             Code      = "TestModule",
//             Name      = "Test Module",
//             CreatedBy = Guid.NewGuid(),
//             Enabled   = true
//         };
//
//         var build = new Build
//         {
//             ModuleId       = module.Id,
//             Version        = "1.0.0",
//             RuntimeVersion = ">=1.0.0",
//             FilePath       = "test-path",
//             Enabled        = true,
//             CreatedBy      = Guid.NewGuid()
//         };
//
//         var fileStream = new MemoryStream();
//
//         _loginUserQueryMock.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
//                           .ReturnsAsync(loginUser);
//
//         _moduleRepositoryMock.Setup(x => x.GetAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Module, bool>>>(), It.IsAny<CancellationToken>()))
//                             .ReturnsAsync(module);
//
//         _buildRepositoryMock.Setup(x => x.Query())
//                            .Returns(_buildQueryableMock.Object);
//
//         _buildQueryableMock.Setup(x => x.Where(It.IsAny<System.Linq.Expressions.Expression<Func<Build, bool>>>()))
//                           .Returns(_buildQueryableMock.Object);
//
//         _buildQueryableMock.Setup(x => x.OrderByDescending(It.IsAny<System.Linq.Expressions.Expression<Func<Build, string>>>()))
//                           .Returns(_buildQueryableMock.Object);
//
//         _buildQueryableMock.Setup(x => x.FirstOrDefaultAsync(It.IsAny<CancellationToken>()))
//                           .ReturnsAsync(build);
//
//         _packageFileServiceMock.Setup(x => x.GetPackageAsync(It.IsAny<PackageKey>(), It.IsAny<CancellationToken>()))
//                               .ReturnsAsync(fileStream);
//
//         // Act
//         var result = await _handler.Handle(command, CancellationToken.None);
//
//         // Assert
//         Assert.NotNull(result);
//         Assert.Equal("TestModule-1.0.0.zip", result.FileName);
//         Assert.Equal("application/zip", result.ContentType);
//         Assert.Equal(fileStream, result.FileStream);
//
//         _downloadCountRepositoryMock.Verify(x => x.AddAsync(It.IsAny<DownloadCount>(), It.IsAny<CancellationToken>()), Times.Once);
//     }
//
//     [Fact]
//     public async Task Handle_UserNotLoggedIn_ShouldThrowAuthorizeException()
//     {
//         // Arrange
//         var command = new DownloadModuleCommand
//         {
//             Code = new Id("TestModule")
//         };
//
//         _loginUserQueryMock.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
//                           .ReturnsAsync((LoginUser?)null);
//
//         // Act & Assert
//         await Assert.ThrowsAsync<AuthorizeException>(() => _handler.Handle(command, CancellationToken.None));
//     }
//
//     [Fact]
//     public async Task Handle_ModuleNotFound_ShouldThrowNotFoundException()
//     {
//         // Arrange
//         var command = new DownloadModuleCommand
//         {
//             Code = new Id("NonExistentModule")
//         };
//
//         var loginUser = new LoginUser { UserId = Guid.NewGuid() };
//
//         _loginUserQueryMock.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
//                           .ReturnsAsync(loginUser);
//
//         _moduleRepositoryMock.Setup(x => x.GetAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Module, bool>>>(), It.IsAny<CancellationToken>()))
//                             .ReturnsAsync((Module?)null);
//
//         // Act & Assert
//         await Assert.ThrowsAsync<NotFoundException>(() => _handler.Handle(command, CancellationToken.None));
//     }
//
//     [Fact]
//     public async Task Handle_ModuleDisabled_ShouldThrowBadRequestException()
//     {
//         // Arrange
//         var command = new DownloadModuleCommand
//         {
//             Code = new Id("DisabledModule")
//         };
//
//         var loginUser = new LoginUser { UserId = Guid.NewGuid() };
//         var module = new Module
//         {
//             Platform  = PlatformType.WindowsDesktop,
//             Code      = "DisabledModule",
//             Name      = "Disabled Module",
//             CreatedBy = Guid.NewGuid(),
//             Enabled   = false // Disabled
//         };
//
//         _loginUserQueryMock.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
//                           .ReturnsAsync(loginUser);
//
//         _moduleRepositoryMock.Setup(x => x.GetAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Module, bool>>>(), It.IsAny<CancellationToken>()))
//                             .ReturnsAsync(module);
//
//         // Act & Assert
//         var exception = await Assert.ThrowsAsync<BadRequestException>(() => _handler.Handle(command, CancellationToken.None));
//         Assert.Contains("已被禁用", exception.Message);
//     }
// }
