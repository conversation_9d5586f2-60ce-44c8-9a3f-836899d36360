using AspNetCore.Abstractions.Exceptions;
using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.PluginCategories.Queries;
using DeviceGuardCloud.Application.Plugins;
using DeviceGuardCloud.Application.Plugins.Commands;
using DeviceGuardCloud.Application.Security.Queries;
using DeviceGuardCloud.Application.Security.Queries.Models;
using DeviceGuardCloud.DomainModel.Plugins;
using Doulex.AspNetCore.Authorization;
using Doulex.DomainDriven;
using Moq;

namespace DeviceGuardCloud.Application.Logic.Tests;

public class CreatePluginCommandHandlerTests
{
    private readonly Mock<IPluginRepository>    _mockPluginRepository;
    private readonly Mock<IPluginValidator>     _mockPluginValidator;
    private readonly Mock<IAuditLogService>     _mockAuditLogService;
    private readonly Mock<IUnitOfWork>          _mockUnitOfWork;
    private readonly Mock<ILoginUserQuery>      _mockLoginUserQuery;
    private readonly Mock<IPluginDbQuery>       _mockPluginDbQuery;
    private readonly Mock<ITransaction>         _mockTransaction;
    private readonly CreatePluginCommandHandler _handler;

    public CreatePluginCommandHandlerTests()
    {
        _mockPluginRepository = new Mock<IPluginRepository>();
        _mockPluginValidator  = new Mock<IPluginValidator>();
        _mockAuditLogService  = new Mock<IAuditLogService>();
        _mockUnitOfWork       = new Mock<IUnitOfWork>();
        _mockLoginUserQuery   = new Mock<ILoginUserQuery>();
        _mockPluginDbQuery    = new Mock<IPluginDbQuery>();
        _mockTransaction      = new Mock<ITransaction>();

        _handler = new CreatePluginCommandHandler(
            _mockPluginRepository.Object,
            _mockPluginValidator.Object,
            _mockAuditLogService.Object,
            _mockUnitOfWork.Object,
            _mockLoginUserQuery.Object,
            _mockPluginDbQuery.Object);
    }

    [Fact]
    public async Task Handle_ShouldCreatePlugin_WhenCommandIsValid()
    {
        // Arrange
        var command = new CreatePluginCommand
        {
            Code        = "test.plugin",
            Name        = "Test Plugin",
            Description = "Test Description",
            Enabled     = true,
            IsFree      = true,
            Host        = PluginHost.DgWin
        };

        var loginUser = new LoginUserModel
        {
            UserId   = Guid.NewGuid(),
            UserName = "testuser"
        };

        _mockLoginUserQuery.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
                           .ReturnsAsync(loginUser);

        _mockPluginRepository.Setup(x => x.GetAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Plugin, bool>>>(), It.IsAny<CancellationToken>()))
                             .ReturnsAsync((Plugin?)null);

        _mockUnitOfWork.Setup(x => x.BeginTransactionAsync(It.IsAny<CancellationToken>()))
                       .ReturnsAsync(_mockTransaction.Object);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotEqual(Guid.Empty, result);
        _mockPluginRepository.Verify(x => x.AddAsync(It.IsAny<Plugin>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockAuditLogService.Verify(x => x.LogAsync(It.IsAny<AuditLog>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockTransaction.Verify(x => x.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockPluginDbQuery.Verify(x => x.ResetCache(), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowAuthorizeException_WhenUserNotLoggedIn()
    {
        // Arrange
        var command = new CreatePluginCommand
        {
            Code = "test.plugin",
            Name = "Test Plugin"
        };

        _mockLoginUserQuery.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
                           .ReturnsAsync((LoginUserModel?)null);

        // Act & Assert
        await Assert.ThrowsAsync<AuthorizeException>(() => _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldThrowBadRequestException_WhenPluginCodeAlreadyExists()
    {
        // Arrange
        var command = new CreatePluginCommand
        {
            Code = "test.plugin",
            Name = "Test Plugin",
            Host = PluginHost.DgWin
        };

        var loginUser = new LoginUserModel
        {
            UserId   = Guid.NewGuid(),
            UserName = "testuser"
        };

        var existingPlugin = new Plugin
        {
            Code      = "test.plugin",
            Name      = "Existing Plugin",
            CreatedBy = Guid.NewGuid()
        };

        _mockLoginUserQuery.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
                           .ReturnsAsync(loginUser);

        _mockPluginRepository.Setup(x => x.GetAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Plugin, bool>>>(), It.IsAny<CancellationToken>()))
                             .ReturnsAsync(existingPlugin);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<BadRequestException>(() => _handler.Handle(command, CancellationToken.None));
        Assert.Contains("插件代码 'test.plugin' 已存在", exception.Message);
    }

    [Fact]
    public async Task Handle_ShouldUseCodeAsName_WhenNameIsNull()
    {
        // Arrange
        var command = new CreatePluginCommand
        {
            Code    = "test.plugin",
            Name    = null,
            Enabled = true,
            Host    = PluginHost.DgWin
        };

        var loginUser = new LoginUserModel
        {
            UserId   = Guid.NewGuid(),
            UserName = "testuser"
        };

        _mockLoginUserQuery.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
                           .ReturnsAsync(loginUser);

        _mockPluginRepository.Setup(x => x.GetAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Plugin, bool>>>(), It.IsAny<CancellationToken>()))
                             .ReturnsAsync((Plugin?)null);

        _mockUnitOfWork.Setup(x => x.BeginTransactionAsync(It.IsAny<CancellationToken>()))
                       .ReturnsAsync(_mockTransaction.Object);

        Plugin? capturedPlugin = null;
        _mockPluginRepository.Setup(x => x.AddAsync(It.IsAny<Plugin>(), It.IsAny<CancellationToken>()))
                             .Callback<Plugin, CancellationToken>((plugin, _) => capturedPlugin = plugin);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(capturedPlugin);
        Assert.Equal("test.plugin", capturedPlugin.Name);
        Assert.Equal("test.plugin", capturedPlugin.Code);
    }

    [Fact]
    public async Task Handle_ShouldSetDefaultPlatform_WhenCreatingPlugin()
    {
        // Arrange
        var command = new CreatePluginCommand
        {
            Code    = "test.plugin",
            Name    = "Test Plugin",
            Enabled = true,
            Host    = PluginHost.DgWin
        };

        var loginUser = new LoginUserModel
        {
            UserId   = Guid.NewGuid(),
            UserName = "testuser"
        };

        _mockLoginUserQuery.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
                           .ReturnsAsync(loginUser);

        _mockPluginRepository.Setup(x => x.GetAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Plugin, bool>>>(), It.IsAny<CancellationToken>()))
                             .ReturnsAsync((Plugin?)null);

        _mockUnitOfWork.Setup(x => x.BeginTransactionAsync(It.IsAny<CancellationToken>()))
                       .ReturnsAsync(_mockTransaction.Object);

        Plugin? capturedPlugin = null;
        _mockPluginRepository.Setup(x => x.AddAsync(It.IsAny<Plugin>(), It.IsAny<CancellationToken>()))
                             .Callback<Plugin, CancellationToken>((plugin, _) => capturedPlugin = plugin);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(capturedPlugin);
        Assert.Equal(PluginHost.DgWin, capturedPlugin.Host);
    }

    [Fact]
    public async Task Handle_ShouldSetIsFreeProperty_WhenCreatingPlugin()
    {
        // Arrange
        var command = new CreatePluginCommand
        {
            Code    = "test.plugin",
            Name    = "Test Plugin",
            Enabled = true,
            IsFree  = false,
            Host    = PluginHost.DgWin
        };

        var loginUser = new LoginUserModel
        {
            UserId   = Guid.NewGuid(),
            UserName = "testuser"
        };

        _mockLoginUserQuery.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
                           .ReturnsAsync(loginUser);

        _mockPluginRepository.Setup(x => x.GetAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Plugin, bool>>>(), It.IsAny<CancellationToken>()))
                             .ReturnsAsync((Plugin?)null);

        _mockUnitOfWork.Setup(x => x.BeginTransactionAsync(It.IsAny<CancellationToken>()))
                       .ReturnsAsync(_mockTransaction.Object);

        Plugin? capturedPlugin = null;
        _mockPluginRepository.Setup(x => x.AddAsync(It.IsAny<Plugin>(), It.IsAny<CancellationToken>()))
                             .Callback<Plugin, CancellationToken>((plugin, _) => capturedPlugin = plugin);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(capturedPlugin);
        Assert.False(capturedPlugin.IsFree);
    }
}
