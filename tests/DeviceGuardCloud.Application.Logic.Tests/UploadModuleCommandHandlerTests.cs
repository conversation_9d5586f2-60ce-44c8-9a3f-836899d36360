// using AspNetCore.Abstractions.Exceptions;
// using DeviceGuardCloud.Application.Audits.Services;
// using DeviceGuardCloud.Application.Builds.Deploys;
// using DeviceGuardCloud.Application.Modules;
// using DeviceGuardCloud.Application.Modules.Commands;
// using DeviceGuardCloud.Application.Security.Queries;
// using DeviceGuardCloud.Application.Security.Queries.Models;
// using DeviceGuardCloud.DomainModel.Builds;
// using DeviceGuardCloud.DomainModel.Modules;
// using Doulex.AspNetCore.Authorization;
// using Doulex.DomainDriven;
// using Moq;
//
// namespace DeviceGuardCloud.Application.Logic.Tests;
//
// public class UploadModuleCommandHandlerTests
// {
//     private readonly Mock<IPackageFileService>   _packageFileServiceMock;
//     private readonly Mock<IModuleRepository>     _moduleRepositoryMock;
//     private readonly Mock<IModuleValidator>      _moduleValidatorMock;
//     private readonly Mock<IBuildRepository>      _buildRepositoryMock;
//     private readonly Mock<IBuildValidator>       _buildValidatorMock;
//     private readonly Mock<ILoginUserQuery>       _loginUserQueryMock;
//     private readonly Mock<IAuditLogService>      _auditLogServiceMock;
//     private readonly Mock<IUnitOfWork>           _unitOfWorkMock;
//     private readonly Mock<IModuleVersionUpdater> _moduleVersionUpdaterMock;
//     private readonly Mock<ITransaction>          _transactionMock;
//
//     private readonly UploadModuleCommandHandler _handler;
//
//     public UploadModuleCommandHandlerTests()
//     {
//         _packageFileServiceMock   = new Mock<IPackageFileService>();
//         _moduleRepositoryMock     = new Mock<IModuleRepository>();
//         _moduleValidatorMock      = new Mock<IModuleValidator>();
//         _buildRepositoryMock      = new Mock<IBuildRepository>();
//         _buildValidatorMock       = new Mock<IBuildValidator>();
//         _loginUserQueryMock       = new Mock<ILoginUserQuery>();
//         _auditLogServiceMock      = new Mock<IAuditLogService>();
//         _unitOfWorkMock           = new Mock<IUnitOfWork>();
//         _moduleVersionUpdaterMock = new Mock<IModuleVersionUpdater>();
//         _transactionMock          = new Mock<ITransaction>();
//
//         _handler = new UploadModuleCommandHandler(
//             _packageFileServiceMock.Object,
//             _moduleRepositoryMock.Object,
//             _buildRepositoryMock.Object,
//             _loginUserQueryMock.Object,
//             _unitOfWorkMock.Object,
//             _moduleValidatorMock.Object,
//             _buildValidatorMock.Object,
//             _auditLogServiceMock.Object,
//             _moduleVersionUpdaterMock.Object);
//     }
//
//     [Fact]
//     public async Task Handle_ValidUpload_ShouldCreateModuleAndBuildSuccessfully()
//     {
//         // Arrange
//         var zipContent = new MemoryStream();
//         var command = new UploadModuleCommand
//         {
//             ZipContent = zipContent
//         };
//
//         var loginUser = new LoginUser { UserId = Guid.NewGuid() };
//         var packageFile = new PackageFile
//         {
//             Key = new PackageKey
//             {
//                 ModuleName = "TestModule",
//                 Version    = "1.0.0",
//                 Platform   = PlatformType.WindowsDesktop
//             },
//             RuntimeVersion = ">=1.0.0",
//             FilePath       = "test-path"
//         };
//
//         _loginUserQueryMock.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
//                           .ReturnsAsync(loginUser);
//
//         _packageFileServiceMock.Setup(x => x.SavePackageAsync(It.IsAny<MemoryStream>(), It.IsAny<CancellationToken>()))
//                               .ReturnsAsync(packageFile);
//
//         _moduleRepositoryMock.Setup(x => x.GetAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Module, bool>>>(), It.IsAny<CancellationToken>()))
//                             .ReturnsAsync((Module?)null);
//
//         _buildRepositoryMock.Setup(x => x.GetAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Build, bool>>>(), It.IsAny<CancellationToken>()))
//                            .ReturnsAsync((Build?)null);
//
//         _unitOfWorkMock.Setup(x => x.BeginTransactionAsync(It.IsAny<CancellationToken>()))
//                       .ReturnsAsync(_transactionMock.Object);
//
//         // Act
//         var result = await _handler.Handle(command, CancellationToken.None);
//
//         // Assert
//         Assert.NotEqual(Guid.Empty, result);
//         
//         _moduleRepositoryMock.Verify(x => x.AddOrUpdateAsync(It.IsAny<Module>(), It.IsAny<CancellationToken>()), Times.Once);
//         _buildRepositoryMock.Verify(x => x.AddOrUpdateAsync(It.IsAny<Build>(), It.IsAny<CancellationToken>()), Times.Once);
//         _unitOfWorkMock.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.AtLeastOnce);
//         _moduleVersionUpdaterMock.Verify(x => x.ApplyLatestVersionAsync(It.IsAny<Guid>(), It.IsAny<CancellationToken>()), Times.Once);
//         _auditLogServiceMock.Verify(x => x.LogAsync(It.IsAny<AuditLog>(), It.IsAny<CancellationToken>()), Times.Once);
//         _transactionMock.Verify(x => x.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
//     }
//
//     [Fact]
//     public async Task Handle_UserNotLoggedIn_ShouldThrowAuthorizeException()
//     {
//         // Arrange
//         var zipContent = new MemoryStream();
//         var command = new UploadModuleCommand
//         {
//             ZipContent = zipContent
//         };
//
//         _loginUserQueryMock.Setup(x => x.GetLoginUserQuery(It.IsAny<CancellationToken>()))
//                           .ReturnsAsync((LoginUser?)null);
//
//         // Act & Assert
//         await Assert.ThrowsAsync<AuthorizeException>(() => _handler.Handle(command, CancellationToken.None));
//     }
//
//     [Fact]
//     public async Task Handle_InvalidVersion_ShouldThrowBadRequestException()
//     {
//         // Arrange
//         var zipContent = new MemoryStream();
//         var command = new UploadModuleCommand
//         {
//             ZipContent = zipContent
//         };
//
//         var loginUser = new LoginUser { UserId = Guid.NewGuid() };
//         var packageFile = new PackageFile
//         {
//             Key = new PackageKey
//             {
//                 ModuleName = "TestModule",
//                 Version    = "invalid-version", // Invalid version
//                 Platform   = PlatformType.WindowsDesktop
//             },
//             RuntimeVersion = ">=1.0.0",
//             FilePath       = "test-path"
//         };
//
//         _loginUserQueryMock.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
//                           .ReturnsAsync(loginUser);
//
//         _packageFileServiceMock.Setup(x => x.SavePackageAsync(It.IsAny<MemoryStream>(), It.IsAny<CancellationToken>()))
//                               .ReturnsAsync(packageFile);
//
//         _moduleRepositoryMock.Setup(x => x.GetAsync(It.IsAny<System.Linq.Expressions.Expression<Func<Module, bool>>>(), It.IsAny<CancellationToken>()))
//                             .ReturnsAsync((Module?)null);
//
//         _unitOfWorkMock.Setup(x => x.BeginTransactionAsync(It.IsAny<CancellationToken>()))
//                       .ReturnsAsync(_transactionMock.Object);
//
//         // Act & Assert
//         var exception = await Assert.ThrowsAsync<BadRequestException>(() => _handler.Handle(command, CancellationToken.None));
//         Assert.Contains("版本号", exception.Message);
//         Assert.Contains("无效", exception.Message);
//     }
// }
