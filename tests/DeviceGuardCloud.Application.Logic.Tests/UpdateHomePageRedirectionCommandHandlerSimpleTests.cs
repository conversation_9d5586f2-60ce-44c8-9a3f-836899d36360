using DeviceGuardCloud.Application.HomePageRedirection;
using DeviceGuardCloud.Application.HomePageRedirection.Commands;
using DeviceGuardCloud.Application.HomePageRedirection.Queries;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Moq;

namespace DeviceGuardCloud.Application.Logic.Tests;

public class UpdateHomePageRedirectionCommandHandlerSimpleTests
{
    private readonly Mock<IDistributedCache> _mockDistributedCache;
    private readonly Mock<IHomePageRedirectionQuery> _mockQuery;
    private readonly Mock<ILogger<UpdateHomePageRedirectionCommandHandler>> _mockLogger;
    private readonly UpdateHomePageRedirectionCommandHandler _handler;

    public UpdateHomePageRedirectionCommandHandlerSimpleTests()
    {
        _mockDistributedCache = new Mock<IDistributedCache>();
        _mockQuery = new Mock<IHomePageRedirectionQuery>();
        _mockLogger = new Mock<ILogger<UpdateHomePageRedirectionCommandHandler>>();
        _handler = new UpdateHomePageRedirectionCommandHandler(_mockDistributedCache.Object, _mockQuery.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task Handle_ShouldUpdateCacheAndClearMemoryCache_WhenCommandIsValid()
    {
        // Arrange
        var command = new UpdateHomePageRedirectionCommand
        {
            RedirectUrl = "admin/index.html",
            Enabled = true
        };

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockDistributedCache.Verify(x => x.SetAsync(
            "HomePageRedirection:Config", 
            It.IsAny<byte[]>(), 
            It.IsAny<DistributedCacheEntryOptions>(),
            It.IsAny<CancellationToken>()), Times.Once);
        
        _mockQuery.Verify(x => x.ClearMemoryCache(), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowArgumentException_WhenRedirectUrlIsNull()
    {
        // Arrange
        var command = new UpdateHomePageRedirectionCommand
        {
            RedirectUrl = null!,
            Enabled = true
        };

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldThrowArgumentException_WhenRedirectUrlIsEmpty()
    {
        // Arrange
        var command = new UpdateHomePageRedirectionCommand
        {
            RedirectUrl = "",
            Enabled = true
        };

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldThrowArgumentException_WhenRedirectUrlIsWhitespace()
    {
        // Arrange
        var command = new UpdateHomePageRedirectionCommand
        {
            RedirectUrl = "   ",
            Enabled = true
        };

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(() => _handler.Handle(command, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_ShouldLogErrorAndRethrow_WhenCacheThrowsException()
    {
        // Arrange
        var command = new UpdateHomePageRedirectionCommand
        {
            RedirectUrl = "admin/index.html",
            Enabled = true
        };

        var exception = new Exception("Cache error");
        _mockDistributedCache.Setup(x => x.SetAsync(It.IsAny<string>(), It.IsAny<byte[]>(), It.IsAny<DistributedCacheEntryOptions>(), It.IsAny<CancellationToken>()))
                            .ThrowsAsync(exception);

        // Act & Assert
        var thrownException = await Assert.ThrowsAsync<Exception>(() => _handler.Handle(command, CancellationToken.None));
        Assert.Equal(exception, thrownException);
    }
}
