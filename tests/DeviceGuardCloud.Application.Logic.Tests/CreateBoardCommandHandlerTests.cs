using DeviceGuardCloud.Application.Audits.Services;
using DeviceGuardCloud.Application.Boards;
using DeviceGuardCloud.Application.Boards.Commands;
using DeviceGuardCloud.Application.Security.Queries;
using DeviceGuardCloud.Application.Security.Queries.Models;
using DeviceGuardCloud.DomainModel.Boards;
using Doulex.DomainDriven;
using Moq;

namespace DeviceGuardCloud.Application.Logic.Tests;

public class CreateBoardCommandHandlerTests
{
    private readonly Mock<IBoardRepository> _mockBoardRepository;
    private readonly Mock<IBoardValidator>  _mockBoardValidator;
    private readonly Mock<IUnitOfWork>      _mockUnitOfWork;
    private readonly Mock<IAuditLogService> _mockAuditLogService;
    private readonly Mock<ILoginUserQuery>  _mockLoginUserQuery;
    private readonly Mock<ITransaction>     _mockTransaction;
    private readonly CreateBoardCommandHandler _handler;

    public CreateBoardCommandHandlerTests()
    {
        _mockBoardRepository = new Mock<IBoardRepository>();
        _mockBoardValidator  = new Mock<IBoardValidator>();
        _mockUnitOfWork      = new Mock<IUnitOfWork>();
        _mockAuditLogService = new Mock<IAuditLogService>();
        _mockLoginUserQuery  = new Mock<ILoginUserQuery>();
        _mockTransaction     = new Mock<ITransaction>();

        _handler = new CreateBoardCommandHandler(
            _mockBoardRepository.Object,
            _mockBoardValidator.Object,
            _mockUnitOfWork.Object,
            _mockAuditLogService.Object,
            _mockLoginUserQuery.Object);
    }

    [Fact]
    public async Task Handle_ShouldCreateBoard_WhenCommandIsValid()
    {
        // Arrange
        var command = new CreateBoardCommand
        {
            Model = "TestModel",
            SerialNumber = "SN123456",
            AgencyName = "Test Agency",
            OwnedBy = Guid.NewGuid(),
            Enabled = true
        };

        var loginUser = new LoginUserModel
        {
            UserId = Guid.NewGuid(),
            UserName = "testuser"
        };

        _mockLoginUserQuery.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
                          .ReturnsAsync(loginUser);
        _mockUnitOfWork.Setup(x => x.BeginTransactionAsync(It.IsAny<CancellationToken>()))
                      .ReturnsAsync(_mockTransaction.Object);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBoardRepository.Verify(x => x.AddAsync(It.Is<Board>(b => 
            b.Model == command.Model &&
            b.SerialNumber == command.SerialNumber &&
            b.AgencyName == command.AgencyName &&
            b.OwnedBy == command.OwnedBy &&
            b.Enabled == command.Enabled &&
            b.CreatedBy == loginUser.UserId &&
            !string.IsNullOrEmpty(b.AesKey)
        ), It.IsAny<CancellationToken>()), Times.Once);

        _mockBoardValidator.Verify(x => x.ValidateAsync(It.IsAny<Board>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(It.IsAny<CancellationToken>()), Times.Once);
        _mockAuditLogService.Verify(x => x.LogAsync(It.IsAny<AuditLog>(), It.IsAny<CancellationToken>()), Times.Once);
        _mockTransaction.Verify(x => x.CommitAsync(It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldCreateBoardWithEnabledFalse_WhenEnabledIsFalse()
    {
        // Arrange
        var command = new CreateBoardCommand
        {
            Model = "TestModel",
            SerialNumber = "SN123456",
            Enabled = false // Explicitly set to false
        };

        var loginUser = new LoginUserModel
        {
            UserId = Guid.NewGuid(),
            UserName = "testuser"
        };

        _mockLoginUserQuery.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
                          .ReturnsAsync(loginUser);
        _mockUnitOfWork.Setup(x => x.BeginTransactionAsync(It.IsAny<CancellationToken>()))
                      .ReturnsAsync(_mockTransaction.Object);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBoardRepository.Verify(x => x.AddAsync(It.Is<Board>(b => 
            b.Enabled == false
        ), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldCreateBoardWithEnabledTrue_WhenEnabledIsDefault()
    {
        // Arrange
        var command = new CreateBoardCommand
        {
            Model = "TestModel",
            SerialNumber = "SN123456"
            // Enabled not explicitly set, should default to true
        };

        var loginUser = new LoginUserModel
        {
            UserId = Guid.NewGuid(),
            UserName = "testuser"
        };

        _mockLoginUserQuery.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
                          .ReturnsAsync(loginUser);
        _mockUnitOfWork.Setup(x => x.BeginTransactionAsync(It.IsAny<CancellationToken>()))
                      .ReturnsAsync(_mockTransaction.Object);

        // Act
        await _handler.Handle(command, CancellationToken.None);

        // Assert
        _mockBoardRepository.Verify(x => x.AddAsync(It.Is<Board>(b => 
            b.Enabled == true // Should default to true
        ), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task Handle_ShouldThrowUnauthorizedAccessException_WhenUserNotLoggedIn()
    {
        // Arrange
        var command = new CreateBoardCommand
        {
            Model = "TestModel",
            SerialNumber = "SN123456"
        };

        _mockLoginUserQuery.Setup(x => x.GetLoginUserAsync(It.IsAny<CancellationToken>()))
                          .ReturnsAsync((LoginUserModel?)null);

        // Act & Assert
        await Assert.ThrowsAsync<UnauthorizedAccessException>(() => _handler.Handle(command, CancellationToken.None));
    }
}
