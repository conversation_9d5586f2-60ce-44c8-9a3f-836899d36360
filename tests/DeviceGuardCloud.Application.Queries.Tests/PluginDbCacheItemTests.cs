using DeviceGuardCloud.Application.PluginCategories.Queries.Tree;
using DeviceGuardCloud.Application.Queries.PluginDb;

namespace DeviceGuardCloud.Application.Queries.Tests;

public class PluginDbCacheItemTests
{
    [Fact]
    public void PluginDbCacheItem_ShouldAllowSettingAllProperties()
    {
        // Arrange
        var treeData = new CategoryNode { Id = "test", Name = "Test Category" };
        var md5Hash  = "ABC123DEF456";
        var dbFile   = new MemoryStream();

        // Act
        var cacheItem = new PluginDbCacheItem
        {
            TreeData = treeData,
            Md5Hash  = md5Hash,
            DbFile   = dbFile,
            Sha1Hash = "ABC123DEF456"
        };

        // Assert
        Assert.Equal(treeData,       cacheItem.TreeData);
        Assert.Equal(md5Hash,        cacheItem.Md5Hash);
        Assert.Equal(dbFile,         cacheItem.DbFile);
        Assert.Equal("ABC123DEF456", cacheItem.Sha1Hash);
    }

    [Fact]
    public void Dispose_ShouldDisposeDbFileStream()
    {
        // Arrange
        var dbFile = new MemoryStream();
        var cacheItem = new PluginDbCacheItem
        {
            DbFile   = dbFile,
            TreeData = null!,
            Md5Hash  = null!,
            Sha1Hash = null!
        };

        // Act
        cacheItem.Dispose();

        // Assert
        Assert.Throws<ObjectDisposedException>(() => dbFile.ReadByte());
    }

    [Fact]
    public void Dispose_ShouldNotThrow_WhenDbFileIsNull()
    {
        // Arrange
        var cacheItem = new PluginDbCacheItem
        {
            DbFile   = null!,
            TreeData = null!,
            Md5Hash  = null!,
            Sha1Hash = null!
        };

        // Act & Assert
        var exception = Record.Exception(() => cacheItem.Dispose());
        Assert.Null(exception);
    }
}
