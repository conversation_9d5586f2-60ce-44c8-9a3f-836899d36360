using DeviceGuardCloud.Application.HomePageRedirection.Queries;
using DeviceGuardCloud.Application.Queries.HomePageRedirection;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Moq;

namespace DeviceGuardCloud.Application.Queries.Tests;

public class HomePageRedirectionQuerySimpleTests
{
    private readonly Mock<IDistributedCache>  _mockDistributedCache;
    private readonly Mock<IMemoryCache>       _mockMemoryCache;
    private readonly HomePageRedirectionQuery _query;

    public HomePageRedirectionQuerySimpleTests()
    {
        _mockDistributedCache = new Mock<IDistributedCache>();
        _mockMemoryCache = new Mock<IMemoryCache>();
        var mockLogger = new Mock<ILogger<HomePageRedirectionQuery>>();
        _query = new HomePageRedirectionQuery(_mockDistributedCache.Object, _mockMemoryCache.Object, mockLogger.Object);
    }

    [Fact]
    public async Task GetConfigurationAsync_ShouldReturnFromMemoryCache_WhenMemoryCacheHasValue()
    {
        // Arrange
        var expectedConfig = new HomePageRedirectionViewModel
        {
            RedirectUrl = "admin/index.html",
            Enabled = true,
            LastUpdated = DateTime.UtcNow
        };

        object? cachedValue = expectedConfig;
        _mockMemoryCache.Setup(x => x.TryGetValue("HomePageRedirection:MemoryConfig", out cachedValue))
                        .Returns(true);

        // Act
        var result = await _query.GetConfigurationAsync(CancellationToken.None);

        // Assert
        Assert.Equal(expectedConfig.RedirectUrl, result.RedirectUrl);
        Assert.Equal(expectedConfig.Enabled, result.Enabled);
        Assert.Equal(expectedConfig.LastUpdated, result.LastUpdated);
    }

    [Fact]
    public async Task GetConfigurationAsync_ShouldReturnDefaultValues_WhenBothCachesEmpty()
    {
        // Arrange
        object? cachedValue = null!;
        _mockMemoryCache.Setup(x => x.TryGetValue("HomePageRedirection:MemoryConfig", out cachedValue))
                        .Returns(false);

        _mockDistributedCache.Setup(x => x.GetAsync("HomePageRedirection:Config", It.IsAny<CancellationToken>()))
                            .ReturnsAsync((byte[]?)null);

        // Act
        var result = await _query.GetConfigurationAsync(CancellationToken.None);

        // Assert
        Assert.Equal("swagger/index.html", result.RedirectUrl);
        Assert.True(result.Enabled);
        Assert.Null(result.LastUpdated);
    }

    [Fact]
    public async Task GetConfigurationAsync_ShouldReturnDefaultConfiguration_WhenCacheThrowsException()
    {
        // Arrange
        object? cachedValue = null!;
        _mockMemoryCache.Setup(x => x.TryGetValue("HomePageRedirection:MemoryConfig", out cachedValue))
                        .Returns(false);

        var exception = new Exception("Cache error");
        _mockDistributedCache.Setup(x => x.GetAsync(It.IsAny<string>(), It.IsAny<CancellationToken>()))
                            .ThrowsAsync(exception);

        // Act
        var result = await _query.GetConfigurationAsync(CancellationToken.None);

        // Assert
        Assert.Equal("swagger/index.html", result.RedirectUrl);
        Assert.True(result.Enabled);
        Assert.Null(result.LastUpdated);
    }

    [Fact]
    public void ClearMemoryCache_ShouldCallRemove()
    {
        // Act
        _query.ClearMemoryCache();

        // Assert
        _mockMemoryCache.Verify(x => x.Remove("HomePageRedirection:MemoryConfig"), Times.Once);
    }
}
