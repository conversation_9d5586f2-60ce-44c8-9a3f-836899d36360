namespace DeviceGuardCloud.DomainModel.Builds;

public static class SemverUtil
{
    public static string NormalizeSemver(string semver)
    {
        // 先提取版本与 pre-release
        var parts        = semver.Split('-', 2); // [0]: main, [1]: pre-release (if any)
        var versionParts = parts[0].Split('.');

        string major = versionParts[0].PadLeft(5, '0');
        string minor = versionParts.Length > 1 ? versionParts[1].PadLeft(5, '0') : "00000";
        string patch = versionParts.Length > 2 ? versionParts[2].PadLeft(5, '0') : "00000";

        string normalized = $"{major}.{minor}.{patch}";

        if (parts.Length > 1)
        {
            var preReleaseParts = parts[1].Split('.');
            var normalizedPre   = string.Join(".", preReleaseParts.Select(p => IsNumeric(p) ? p.Pad<PERSON>(5, '0') : p));
            return $"{normalized}-{normalizedPre}";
        }
        else
        {
            return $"{normalized}-stable"; // release 版本排在所有 pre-release 后
        }
    }

    private static bool IsNumeric(string s) => int.TryParse(s, out _);
}
